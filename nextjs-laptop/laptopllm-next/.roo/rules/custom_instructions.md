# Custom Instructions for Roo: LLM Laptop Lens Project Guidelines

As Roo, you are an expert software engineer working on the LLM Laptop Lens project. Your primary goal is to adhere strictly to the following project guidelines, derived from our `.clinerules` documentation.

## 1. Core Principles & Philosophy

*   **Prioritize Refactoring & Quality:** Always focus on improving existing code, fixing bugs, and aligning with `clinerules` before adding new features.
*   **Analysis First:** Before any significant change, analyze existing code, tests, and documentation.
*   **No Code Duplication:** Strive for reusable solutions.
*   **Consistency is Key:** Mimic existing style, structure, and patterns.

## 2. Code Quality & Style (Ref: 01-code-quality-and-style.md)

*   **Naming Conventions:**
    *   Variables/Functions: `camelCase`.
    *   Classes/React Components: `PascalCase`.
    *   Global Constants: `SCREAMING_SNAKE_CASE`.
    *   Files: `kebab-case` for components/modules (e.g., `user-card.tsx`).
*   **Formatting:**
    *   Indentation: 2 spaces.
    *   Quotes: Prefer single quotes (`'`).
    *   Line Length: Keep lines within 100-120 characters.
*   **Readability:**
    *   Comments: Explain complex logic or non-obvious decisions.
    *   Docstrings (JSDoc/TSDoc): Document public functions, classes, interfaces, and types.
    *   Clarity: Write self-explanatory code; refactor long functions into smaller, single-purpose ones (SRP).
*   **TypeScript:** Always use strict typing; avoid using global variables and `any`.

## 3. Architecture & Structure (Ref: 02-architecture-and-component-structure.md)

*   **Monorepo:** The project is structured as a monorepo, with `pnpm` as the package manager.
*   **Directory Organization:** Follow feature-based structure (e.g., `src/features/`).
*   **Component Patterns:**
    *   Use Atomic Design (`atoms/`, `molecules/`, `organisms/`).
    *   Separate Presentation from Business Logic (components should be "dumb").
    *   Prioritize functional components with hooks.
*   **Hooks Modularity:** Avoid monolithic hooks. Extract distinct functionalities into smaller, focused, reusable hooks.
*   **Layered Architecture:** Respect presentation, business, and data layers.

## 4. Data & API Handling (Ref: 03-data-and-api-handling.md)

*   **Centralization:** Centralize data fetching logic in services or custom hooks (e.g., React Query).
*   **Validation:** Implement robust data validation (e.g., Zod/Yup).
*   **Error Handling:** Provide clear error messages; consider `Error Boundaries`.
*   **Type Safety:** Ensure API contracts are type-safe.

## 5. Testing (Ref: 04-testing.md)

*   **Coverage:** Strive for high test coverage (80%+ for business logic, 60% for UI).
*   **Frameworks:** Use Vitest and React Testing Library.
*   **Mocks:** Employ mocks for external dependencies.

## 6. Documentation (Ref: 05-documentation.md)

*   **Memory Bank:** Always refer to and update `memory-bank/` files for project context.
*   **In-Code Documentation:** Use JSDoc/TSDoc for public APIs.
*   **READMEs:** Maintain clear `README.md` files for significant directories/modules.

## 7. Performance (Ref: 06-performance-and-optimization.md)

*   **Optimizations:** Apply memoization (`React.memo`, `useCallback`, `useMemo`), lazy loading, and code splitting.
*   **Bundle Size:** Be mindful of bundle size.

## 8. Security (Ref: 07-security.md)

*   **Credentials:** Never hardcode sensitive information; use environment variables.
*   **Input Validation:** Always validate and sanitize user inputs.
*   **Dependency Audits:** Be aware of vulnerable dependencies.

## 9. Git & Workflow (Ref: 10-git-and-version-control.md)

*   **Commit Messages:** Follow Conventional Commits (e.g., `feat:`, `fix:`, `chore:`).
*   **Branch Naming:** Use consistent branch naming conventions (e.g., `feature/`, `fix/`).
*   **Git Hooks:** Leverage pre-commit/pre-push hooks for quality checks.

## 10. Dependency Management (Ref: 11-dependency-management.md)

*   **Package Manager:** Always use `pnpm` for package management.
*   **package.json:** Maintain explicit versions and clear scripts.
*   **Audits:** Conduct regular security audits.

---

**How Roo should use these instructions:**

*   **When generating code:** Apply all relevant naming, formatting, and architectural patterns.
*   **When refactoring:** Identify deviations from these rules and propose corrections.
*   **When analyzing:** Use these rules as a lens to evaluate code quality and identify areas for improvement.
*   **When planning:** Incorporate steps to ensure adherence to these guidelines.
*   **When asked for advice:** Provide guidance based on these established best practices.