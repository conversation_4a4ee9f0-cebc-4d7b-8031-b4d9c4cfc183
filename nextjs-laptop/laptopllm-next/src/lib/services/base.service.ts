// Base service class for database operations

import { PrismaClient } from '../../../generated/prisma'
import { prisma } from '../prisma'
import type { PaginationParams, SearchResults } from '@/shared/types'

export abstract class BaseService {
  protected db: PrismaClient

  constructor() {
    this.db = prisma
  }

  /**
   * Create paginated results with metadata
   */
  protected createPaginatedResults<T>(
    data: T[],
    total: number,
    pagination: PaginationParams
  ): SearchResults<T> {
    const { page, limit } = pagination
    const totalPages = Math.ceil(total / limit)

    return {
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    }
  }

  /**
   * Calculate offset for pagination
   */
  protected getOffset(page: number, limit: number): number {
    return (page - 1) * limit
  }

  /**
   * Validate pagination parameters
   */
  protected validatePagination(pagination: PaginationParams): void {
    if (pagination.page < 1) {
      throw new Error('Page must be greater than 0')
    }
    if (pagination.limit < 1 || pagination.limit > 100) {
      throw new Error('Limit must be between 1 and 100')
    }
  }

  /**
   * Handle database errors with proper error messages
   */
  protected handleDatabaseError(error: unknown, operation: string): never {
    console.error(`Database error in ${operation}:`, error)
    
    if (error instanceof Error) {
      // Handle specific Prisma errors
      if (error.message.includes('Unique constraint')) {
        throw new Error('A record with this data already exists')
      }
      if (error.message.includes('Foreign key constraint')) {
        throw new Error('Referenced record does not exist')
      }
      if (error.message.includes('Record to update not found')) {
        throw new Error('Record not found')
      }
      
      throw new Error(`Database operation failed: ${error.message}`)
    }
    
    throw new Error(`Unknown database error in ${operation}`)
  }

  /**
   * Execute a database operation with error handling
   */
  protected async executeWithErrorHandling<T>(
    operation: () => Promise<T>,
    operationName: string
  ): Promise<T> {
    try {
      return await operation()
    } catch (error) {
      this.handleDatabaseError(error, operationName)
    }
  }

  /**
   * Build dynamic where clause for filtering
   */
  protected buildWhereClause(filters: Record<string, any>): Record<string, any> {
    const where: Record<string, any> = {}

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value) && value.length > 0) {
          where[key] = { in: value }
        } else if (typeof value === 'object' && value.min !== undefined && value.max !== undefined) {
          where[key] = {
            gte: value.min,
            lte: value.max,
          }
        } else if (typeof value === 'string' && value.trim() !== '') {
          where[key] = { contains: value, mode: 'insensitive' }
        } else {
          where[key] = value
        }
      }
    })

    return where
  }

  /**
   * Build order by clause for sorting
   */
  protected buildOrderByClause(
    sortBy?: string,
    sortOrder: 'asc' | 'desc' = 'desc'
  ): Record<string, any>[] | undefined {
    if (!sortBy) return undefined

    const orderBy: Record<string, any> = {}
    
    // Handle nested sorting (e.g., 'brand.name')
    if (sortBy.includes('.')) {
      const [relation, field] = sortBy.split('.')
      orderBy[relation] = { [field]: sortOrder }
    } else {
      orderBy[sortBy] = sortOrder
    }

    return [orderBy]
  }

  /**
   * Execute transaction with retry logic
   */
  protected async executeTransaction<T>(
    operations: (tx: PrismaClient) => Promise<T>,
    maxRetries: number = 3
  ): Promise<T> {
    let lastError: Error | null = null

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await this.db.$transaction(operations)
      } catch (error) {
        lastError = error as Error
        
        // Don't retry on certain errors
        if (error instanceof Error) {
          if (error.message.includes('Unique constraint') ||
              error.message.includes('Foreign key constraint')) {
            throw error
          }
        }

        if (attempt === maxRetries) {
          break
        }

        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 100))
      }
    }

    throw lastError || new Error('Transaction failed after retries')
  }

  /**
   * Check if a record exists
   */
  protected async recordExists(
    model: string,
    where: Record<string, any>
  ): Promise<boolean> {
    try {
      const count = await (this.db as any)[model].count({ where })
      return count > 0
    } catch (error) {
      this.handleDatabaseError(error, `checking if ${model} exists`)
    }
  }

  /**
   * Get record by ID with error handling
   */
  protected async getById<T>(
    model: string,
    id: number,
    include?: Record<string, any>
  ): Promise<T | null> {
    try {
      return await (this.db as any)[model].findUnique({
        where: { id },
        include,
      })
    } catch (error) {
      this.handleDatabaseError(error, `getting ${model} by ID`)
    }
  }

  /**
   * Soft delete a record (if the model supports it)
   */
  protected async softDelete(
    model: string,
    id: number
  ): Promise<void> {
    try {
      await (this.db as any)[model].update({
        where: { id },
        data: { deletedAt: new Date() },
      })
    } catch (error) {
      this.handleDatabaseError(error, `soft deleting ${model}`)
    }
  }

  /**
   * Bulk operations helper
   */
  protected async bulkOperation<T>(
    operation: 'create' | 'update' | 'delete',
    model: string,
    data: any[],
    batchSize: number = 100
  ): Promise<T[]> {
    const results: T[] = []
    
    for (let i = 0; i < data.length; i += batchSize) {
      const batch = data.slice(i, i + batchSize)
      
      try {
        let batchResults: T[]
        
        switch (operation) {
          case 'create':
            batchResults = await (this.db as any)[model].createMany({
              data: batch,
              skipDuplicates: true,
            })
            break
          case 'update':
            batchResults = await Promise.all(
              batch.map((item: any) =>
                (this.db as any)[model].update({
                  where: { id: item.id },
                  data: item,
                })
              )
            )
            break
          case 'delete':
            batchResults = await Promise.all(
              batch.map((id: number) =>
                (this.db as any)[model].delete({
                  where: { id },
                })
              )
            )
            break
          default:
            throw new Error(`Unsupported bulk operation: ${operation}`)
        }
        
        results.push(...batchResults)
      } catch (error) {
        console.error(`Bulk ${operation} error for batch ${i / batchSize + 1}:`, error)
        // Continue with next batch instead of failing completely
      }
    }
    
    return results
  }
}
