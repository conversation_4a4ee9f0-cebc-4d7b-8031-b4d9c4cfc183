// Scraping orchestrator that coordinates Firecrawl and Puppeteer services

import { BaseService } from '../services/base.service'
import { FirecrawlService, type LaptopScrapingTarget, type LaptopData } from './firecrawl.service'
import { PuppeteerService } from './puppeteer.service'
import type { ScrapingTarget, ScrapedData } from './base-scraper'
import { LaptopDataProcessor, type ProcessedLaptopData } from '@/lib/data-processing/laptop-data-processor'
import { MetricsTracker, type ScrapingMetrics } from '@/lib/data-processing/metrics-tracker'

export interface ScrapingStrategy {
  primary: 'firecrawl' | 'puppeteer'
  fallback: 'firecrawl' | 'puppeteer' | 'none'
  retryWithFallback: boolean
}

export interface ScrapingJobConfig {
  strategy: ScrapingStrategy
  batchSize: number
  maxConcurrent: number
  delayBetweenBatches: number
  enableScreenshots: boolean
  saveToDatabase: boolean
}

export interface ScrapingJobResult {
  jobId: string
  status: 'running' | 'completed' | 'failed' | 'cancelled'
  progress: {
    total: number
    completed: number
    failed: number
    percentage: number
  }
  results: {
    successful: LaptopData[]
    failed: Array<{ url: string; error: string }>
  }
  metrics: {
    startTime: Date
    endTime?: Date
    duration?: number
    averageTimePerUrl?: number
    successRate: number
  }
}

export class ScrapingOrchestrator extends BaseService {
  private firecrawlService: FirecrawlService
  private puppeteerService: PuppeteerService
  private dataProcessor: LaptopDataProcessor
  private metricsTracker: MetricsTracker
  private activeJobs: Map<string, ScrapingJobResult> = new Map()

  constructor() {
    super()
    this.firecrawlService = new FirecrawlService()
    this.puppeteerService = new PuppeteerService()
    this.dataProcessor = new LaptopDataProcessor()
    this.metricsTracker = new MetricsTracker()
  }

  /**
   * Start a comprehensive laptop scraping job
   */
  async startLaptopScrapingJob(
    targets: LaptopScrapingTarget[],
    config: Partial<ScrapingJobConfig> = {}
  ): Promise<string> {
    const jobConfig: ScrapingJobConfig = {
      strategy: {
        primary: 'firecrawl',
        fallback: 'puppeteer',
        retryWithFallback: true,
      },
      batchSize: 10,
      maxConcurrent: 3,
      delayBetweenBatches: 2000,
      enableScreenshots: false,
      saveToDatabase: true,
      ...config,
    }

    const jobId = this.generateJobId()
    const jobResult: ScrapingJobResult = {
      jobId,
      status: 'running',
      progress: {
        total: targets.length,
        completed: 0,
        failed: 0,
        percentage: 0,
      },
      results: {
        successful: [],
        failed: [],
      },
      metrics: {
        startTime: new Date(),
        successRate: 0,
      },
    }

    this.activeJobs.set(jobId, jobResult)

    // Start job in background
    this.runScrapingJob(targets, jobConfig, jobResult, jobId).catch(error => {
      console.error(`Scraping job ${jobId} failed:`, error)
      jobResult.status = 'failed'
    })

    return jobId
  }

  /**
   * Get job status and results
   */
  getJobStatus(jobId: string): ScrapingJobResult | null {
    return this.activeJobs.get(jobId) || null
  }

  /**
   * Cancel a running job
   */
  async cancelJob(jobId: string): Promise<boolean> {
    const job = this.activeJobs.get(jobId)
    if (!job || job.status !== 'running') {
      return false
    }

    job.status = 'cancelled'
    job.metrics.endTime = new Date()
    job.metrics.duration = job.metrics.endTime.getTime() - job.metrics.startTime.getTime()

    return true
  }

  /**
   * Scrape a single laptop product with fallback strategy
   */
  async scrapeLaptopWithFallback(
    target: LaptopScrapingTarget,
    strategy: ScrapingStrategy = {
      primary: 'firecrawl',
      fallback: 'puppeteer',
      retryWithFallback: true,
    }
  ): Promise<LaptopData> {
    let lastError: Error | null = null

    // Try primary strategy
    try {
      if (strategy.primary === 'firecrawl') {
        return await this.firecrawlService.scrapeLaptopProduct(target)
      } else {
        return await this.puppeteerService.scrapeLaptopProduct(target)
      }
    } catch (error) {
      lastError = error as Error
      console.warn(`Primary scraping method (${strategy.primary}) failed for ${target.url}:`, error)
    }

    // Try fallback strategy if enabled
    if (strategy.retryWithFallback && strategy.fallback !== 'none') {
      try {
        console.log(`Trying fallback method (${strategy.fallback}) for ${target.url}`)
        
        if (strategy.fallback === 'firecrawl') {
          return await this.firecrawlService.scrapeLaptopProduct(target)
        } else {
          return await this.puppeteerService.scrapeLaptopProduct(target)
        }
      } catch (fallbackError) {
        console.error(`Fallback scraping method (${strategy.fallback}) also failed for ${target.url}:`, fallbackError)
        lastError = fallbackError as Error
      }
    }

    throw lastError || new Error('All scraping methods failed')
  }

  /**
   * Discover laptop URLs from e-commerce sites
   */
  async discoverLaptopUrls(
    sites: Array<{
      baseUrl: string
      siteType: LaptopScrapingTarget['siteType']
      searchPaths?: string[]
      maxPages?: number
    }>
  ): Promise<LaptopScrapingTarget[]> {
    const discoveredTargets: LaptopScrapingTarget[] = []

    for (const site of sites) {
      try {
        console.log(`Discovering laptop URLs from ${site.baseUrl}`)

        // Use Firecrawl to crawl and discover URLs
        const urls = await this.firecrawlService.crawlForLaptops(site.baseUrl, {
          maxPages: site.maxPages || 50,
          includePaths: site.searchPaths || ['/laptop', '/notebook', '/gaming'],
          laptopKeywords: ['laptop', 'notebook', 'gaming', 'ultrabook', 'chromebook'],
        })

        const targets: LaptopScrapingTarget[] = urls.map(url => ({
          url,
          siteType: site.siteType,
        }))

        discoveredTargets.push(...targets)
        console.log(`Discovered ${targets.length} laptop URLs from ${site.baseUrl}`)

      } catch (error) {
        console.error(`Failed to discover URLs from ${site.baseUrl}:`, error)
      }
    }

    return discoveredTargets
  }

  /**
   * Search for laptops across multiple sources
   */
  async searchLaptopsAcrossSources(
    query: string,
    options: {
      maxResultsPerSource?: number
      sources?: Array<'web' | 'amazon' | 'bestbuy' | 'newegg'>
    } = {}
  ): Promise<LaptopData[]> {
    const results: LaptopData[] = []
    const maxResults = options.maxResultsPerSource || 10
    const sources = options.sources || ['web']

    for (const source of sources) {
      try {
        let sourceResults: LaptopData[] = []

        switch (source) {
          case 'web':
            sourceResults = await this.firecrawlService.searchLaptops(query, {
              limit: maxResults,
            })
            break

          case 'amazon':
            sourceResults = await this.firecrawlService.searchLaptops(
              `${query} site:amazon.com`,
              { limit: maxResults }
            )
            break

          case 'bestbuy':
            sourceResults = await this.firecrawlService.searchLaptops(
              `${query} site:bestbuy.com`,
              { limit: maxResults }
            )
            break

          case 'newegg':
            sourceResults = await this.firecrawlService.searchLaptops(
              `${query} site:newegg.com`,
              { limit: maxResults }
            )
            break
        }

        results.push(...sourceResults)
        console.log(`Found ${sourceResults.length} laptops from ${source}`)

      } catch (error) {
        console.error(`Failed to search ${source} for "${query}":`, error)
      }
    }

    return results
  }

  /**
   * Save scraped laptop data to database with processing, validation, and metrics tracking
   */
  async saveLaptopData(laptopData: LaptopData[], sourceId?: string, jobId?: string): Promise<{
    processed: number
    successful: number
    newLaptops: number
    updatedLaptops: number
    duplicates: number
    averageConfidence: number
  }> {
    return this.executeWithErrorHandling(async () => {
      const startTime = Date.now()

      // Process and validate data
      const processedData = await this.dataProcessor.processLaptopData(laptopData)

      console.log(`Processing ${laptopData.length} raw laptops → ${processedData.length} validated laptops`)

      let newLaptops = 0
      let updatedLaptops = 0
      let successful = 0
      let duplicates = 0
      const confidenceScores: number[] = []
      const errors: string[] = []

      for (const laptop of processedData) {
        try {
          // Track confidence scores
          confidenceScores.push(laptop.confidence)

          // Check for duplicates
          if (laptop.processingMetadata.duplicateScore && laptop.processingMetadata.duplicateScore > 0.8) {
            duplicates++
            continue
          }

          // Check if laptop already exists using fingerprint and normalized data
          const existingLaptop = await this.findExistingLaptop(laptop)

          if (existingLaptop) {
            // Update existing laptop
            await this.updateExistingLaptop(existingLaptop.id, laptop)
            updatedLaptops++
            console.log(`Updated existing laptop: ${laptop.normalizedTitle} (confidence: ${laptop.confidence})`)
          } else {
            // Create new laptop
            await this.createNewLaptop(laptop)
            newLaptops++
            console.log(`Created new laptop: ${laptop.normalizedTitle} (confidence: ${laptop.confidence})`)
          }

          successful++
        } catch (error) {
          const errorMsg = `Failed to save laptop data for ${laptop.normalizedTitle}: ${error.message}`
          console.error(errorMsg)
          errors.push(errorMsg)
        }
      }

      const processingTimeMs = Date.now() - startTime
      const averageConfidence = confidenceScores.length > 0
        ? confidenceScores.reduce((sum, score) => sum + score, 0) / confidenceScores.length
        : 0

      // Track metrics if job info is provided
      if (sourceId && jobId) {
        const metrics: ScrapingMetrics = {
          sourceId,
          jobId,
          startTime: new Date(startTime),
          endTime: new Date(),
          itemsDiscovered: laptopData.length,
          itemsProcessed: processedData.length,
          itemsSuccessful: successful,
          itemsFailed: processedData.length - successful,
          duplicatesFound: duplicates,
          newLaptopsCreated: newLaptops,
          existingLaptopsUpdated: updatedLaptops,
          averageConfidence,
          processingTimeMs,
          errorMessages: errors,
        }

        await this.metricsTracker.completeJobTracking(metrics)
      }

      return {
        processed: processedData.length,
        successful,
        newLaptops,
        updatedLaptops,
        duplicates,
        averageConfidence,
      }
    }, 'saveLaptopData')
  }

  /**
   * Clean up completed jobs (keep only recent ones)
   */
  cleanupCompletedJobs(maxAge: number = 24 * 60 * 60 * 1000): void {
    const now = Date.now()
    
    for (const [jobId, job] of this.activeJobs.entries()) {
      if (job.status !== 'running') {
        const jobAge = now - job.metrics.startTime.getTime()
        if (jobAge > maxAge) {
          this.activeJobs.delete(jobId)
        }
      }
    }
  }

  // Private helper methods

  private async runScrapingJob(
    targets: LaptopScrapingTarget[],
    config: ScrapingJobConfig,
    jobResult: ScrapingJobResult,
    jobId: string
  ): Promise<void> {
    const batches = this.createBatches(targets, config.batchSize)

    // Start metrics tracking
    const sourceId = targets.length > 0 ? this.extractSourceIdFromUrl(targets[0].url) : 'unknown'
    await this.metricsTracker.startJobTracking(sourceId, jobId)

    for (let i = 0; i < batches.length; i++) {
      if (jobResult.status === 'cancelled') {
        break
      }

      const batch = batches[i]
      console.log(`Processing batch ${i + 1}/${batches.length} (${batch.length} URLs)`)

      // Process batch with concurrency limit
      const batchPromises = batch.map(target =>
        this.scrapeLaptopWithFallback(target, config.strategy)
          .then(result => ({ success: true, data: result, url: target.url }))
          .catch(error => ({ success: false, error: error.message, url: target.url }))
      )

      const batchResults = await Promise.all(batchPromises)

      // Update job results
      for (const result of batchResults) {
        if (result.success) {
          jobResult.results.successful.push(result.data as LaptopData)
          jobResult.progress.completed++
        } else {
          jobResult.results.failed.push({ url: result.url, error: result.error as string })
          jobResult.progress.failed++
        }
      }

      // Update progress
      jobResult.progress.percentage = Math.round(
        ((jobResult.progress.completed + jobResult.progress.failed) / jobResult.progress.total) * 100
      )

      // Save to database if enabled
      if (config.saveToDatabase) {
        const successfulResults = batchResults
          .filter(r => r.success)
          .map(r => r.data as LaptopData)

        if (successfulResults.length > 0) {
          // Extract sourceId from the first target (assuming all targets in a job are from the same source)
          const sourceId = targets.length > 0 ? this.extractSourceIdFromUrl(targets[0].url) : undefined
          const saveResult = await this.saveLaptopData(successfulResults, sourceId, jobId)

          // Update job progress with save results
          await this.metricsTracker.updateJobProgress(jobId, sourceId || 'unknown', {
            itemsProcessed: saveResult.processed,
            itemsSuccessful: saveResult.successful,
            phase: 'database_save',
            message: `Saved ${saveResult.successful}/${saveResult.processed} laptops (${saveResult.newLaptops} new, ${saveResult.updatedLaptops} updated)`,
          })
        }
      }

      // Delay between batches
      if (i < batches.length - 1 && config.delayBetweenBatches > 0) {
        await new Promise(resolve => setTimeout(resolve, config.delayBetweenBatches))
      }
    }

    // Finalize job
    jobResult.status = 'completed'
    jobResult.metrics.endTime = new Date()
    jobResult.metrics.duration = jobResult.metrics.endTime.getTime() - jobResult.metrics.startTime.getTime()
    jobResult.metrics.averageTimePerUrl = jobResult.metrics.duration / targets.length
    jobResult.metrics.successRate = jobResult.progress.completed / jobResult.progress.total
  }

  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = []
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize))
    }
    return batches
  }

  private generateJobId(): string {
    return `scraping_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Extract source ID from URL (simplified - in production this would be more sophisticated)
   */
  private extractSourceIdFromUrl(url: string): string {
    try {
      const domain = new URL(url).hostname.replace('www.', '')
      return domain.replace(/\./g, '_')
    } catch {
      return 'unknown_source'
    }
  }

  /**
   * Find existing laptop using multiple strategies
   */
  private async findExistingLaptop(laptop: ProcessedLaptopData): Promise<any> {
    // Strategy 1: Exact fingerprint match
    const fingerprintMatch = await this.db.laptops.findFirst({
      where: {
        // We'll need to add a fingerprint field to the database schema
        // For now, use model_name as a proxy
        model_name: laptop.normalizedTitle,
      },
    })

    if (fingerprintMatch) {
      return fingerprintMatch
    }

    // Strategy 2: Brand + model match
    if (laptop.normalizedBrand && laptop.normalizedModel) {
      const brandModelMatch = await this.db.laptops.findFirst({
        where: {
          AND: [
            { brands: { name: { equals: laptop.normalizedBrand, mode: 'insensitive' } } },
            { model_name: { contains: laptop.normalizedModel, mode: 'insensitive' } },
          ],
        },
        include: { brands: true },
      })

      if (brandModelMatch) {
        return brandModelMatch
      }
    }

    // Strategy 3: Fuzzy title match
    const titleMatches = await this.db.laptops.findMany({
      where: {
        model_name: { contains: laptop.normalizedBrand, mode: 'insensitive' },
      },
      take: 5,
      include: { brands: true },
    })

    // Check for high similarity
    for (const match of titleMatches) {
      const similarity = this.calculateTitleSimilarity(laptop.normalizedTitle, match.model_name)
      if (similarity > 0.8) { // 80% similarity threshold
        return match
      }
    }

    return null
  }

  /**
   * Calculate title similarity
   */
  private calculateTitleSimilarity(title1: string, title2: string): number {
    const words1 = title1.toLowerCase().split(' ')
    const words2 = title2.toLowerCase().split(' ')

    const commonWords = words1.filter(word => words2.includes(word))
    const totalWords = Math.max(words1.length, words2.length)

    return commonWords.length / totalWords
  }

  private async updateExistingLaptop(laptopId: number, laptopData: ProcessedLaptopData): Promise<void> {
    // Update laptop basic information
    await this.db.laptops.update({
      where: { id: laptopId },
      data: {
        model_name: laptopData.normalizedTitle,
        description: laptopData.description || undefined,
        updated_at: new Date(),
      },
    })
    // Update laptop listings with new price information
    if (laptopData.price) {
      await this.db.laptop_listings.upsert({
        where: {
          laptop_id_source_id: {
            laptop_id: laptopId,
            source_id: 1, // Default source ID, should be dynamic
          },
        },
        update: {
          price: laptopData.price.current,
          currency: laptopData.price.currency,
          availability_status: laptopData.availability?.inStock ? 'in_stock' : 'out_of_stock',
          last_seen: new Date(),
        },
        create: {
          laptop_id: laptopId,
          source_id: 1,
          url: laptopData.url,
          price: laptopData.price.current,
          currency: laptopData.price.currency,
          availability_status: laptopData.availability?.inStock ? 'in_stock' : 'out_of_stock',
          last_seen: new Date(),
        },
      })
    }

    // Update specifications if available and different
    if (laptopData.specifications) {
      await this.updateLaptopSpecifications(laptopId, laptopData.specifications)
    }
  }

  private async createNewLaptop(laptopData: ProcessedLaptopData): Promise<void> {
    try {
      // Use normalized brand name
      const brandName = laptopData.normalizedBrand

      // Find or create brand
      let brand = await this.db.brands.findFirst({
        where: { name: { equals: brandName, mode: 'insensitive' } },
      })

      if (!brand) {
        brand = await this.db.brands.create({
          data: {
            name: brandName,
            website: this.getBrandWebsite(brandName),
            country: 'Unknown',
          },
        })
      }

      // Create the laptop with normalized data
      const laptop = await this.db.laptops.create({
        data: {
          model_name: laptopData.normalizedTitle,
          brand_id: brand.id,
          description: laptopData.description || '',
          release_date: laptopData.releaseDate ? new Date(laptopData.releaseDate) : null,
          weight: laptopData.specifications?.weight || null,
          dimensions: laptopData.specifications?.dimensions || null,
          created_at: new Date(),
          updated_at: new Date(),
        },
      })

      // Create laptop listing with price and availability
      if (laptopData.price) {
        await this.db.laptop_listings.create({
          data: {
            laptop_id: laptop.id,
            source_id: 1, // Default source ID - should be dynamic based on actual source
            url: laptopData.url,
            price: laptopData.price.current,
            currency: laptopData.price.currency || 'USD',
            availability_status: laptopData.availability?.inStock ? 'in_stock' : 'out_of_stock',
            last_seen: new Date(),
          },
        })
      }

      // Add specifications if available
      if (laptopData.specifications) {
        await this.createLaptopSpecifications(laptop.id, laptopData.specifications)
      }

      console.log(`Created new laptop: ${laptopData.normalizedTitle} (ID: ${laptop.id}, confidence: ${laptopData.confidence})`)
    } catch (error) {
      console.error(`Failed to create new laptop ${laptopData.normalizedTitle}:`, error)
      throw error
    }
  }

  /**
   * Update laptop specifications (for existing laptops)
   */
  private async updateLaptopSpecifications(laptopId: number, specs: any): Promise<void> {
    try {
      // For updates, we'll only update if the new specs are more complete
      // This is a simplified version - in production you'd want more sophisticated merging logic

      if (specs.cpu) {
        const cpu = await this.findOrCreateCpu(specs.cpu)
        if (cpu) {
          // Check if CPU association already exists
          const existingCpu = await this.db.laptop_cpus.findFirst({
            where: { laptop_id: laptopId },
          })

          if (!existingCpu) {
            await this.db.laptop_cpus.create({
              data: {
                laptop_id: laptopId,
                cpu_id: cpu.id,
                quantity: 1,
              },
            })
          }
        }
      }

      // Similar logic for GPU, RAM, and Storage...
      // Simplified for now to avoid making this too long

    } catch (error) {
      console.error(`Failed to update specifications for laptop ${laptopId}:`, error)
    }
  }

  private async createLaptopSpecifications(laptopId: number, specs: any): Promise<void> {
    try {
      // Handle CPU specifications
      if (specs.cpu) {
        const cpu = await this.findOrCreateCpu(specs.cpu)
        if (cpu) {
          await this.db.laptop_cpus.create({
            data: {
              laptop_id: laptopId,
              cpu_id: cpu.id,
              quantity: 1,
            },
          })
        }
      }

      // Handle GPU specifications
      if (specs.gpu) {
        const gpu = await this.findOrCreateGpu(specs.gpu)
        if (gpu) {
          await this.db.laptop_gpus.create({
            data: {
              laptop_id: laptopId,
              gpu_id: gpu.id,
              quantity: 1,
            },
          })
        }
      }

      // Handle RAM specifications
      if (specs.memory) {
        const ramConfig = await this.findOrCreateRamConfiguration(specs.memory)
        if (ramConfig) {
          await this.db.laptop_ram.create({
            data: {
              laptop_id: laptopId,
              ram_configuration_id: ramConfig.id,
              slots_used: 1,
              max_capacity: specs.memory.maxCapacity || specs.memory.size,
            },
          })
        }
      }

      // Handle Storage specifications
      if (specs.storage) {
        const storageDevice = await this.findOrCreateStorageDevice(specs.storage)
        if (storageDevice) {
          await this.db.laptop_storage.create({
            data: {
              laptop_id: laptopId,
              storage_device_id: storageDevice.id,
              quantity: 1,
            },
          })
        }
      }
    } catch (error) {
      console.error(`Failed to create specifications for laptop ${laptopId}:`, error)
    }
  }

  private extractBrandFromTitle(title: string): string {
    const commonBrands = ['Apple', 'Dell', 'HP', 'Lenovo', 'ASUS', 'Acer', 'MSI', 'Razer', 'Alienware', 'Samsung', 'LG', 'Microsoft', 'Google', 'Framework']

    for (const brand of commonBrands) {
      if (title.toLowerCase().includes(brand.toLowerCase())) {
        return brand
      }
    }

    // Extract first word as potential brand
    const firstWord = title.split(' ')[0]
    return firstWord || 'Unknown'
  }

  private getBrandWebsite(brandName: string): string {
    const brandWebsites: Record<string, string> = {
      'Apple': 'https://www.apple.com',
      'Dell': 'https://www.dell.com',
      'HP': 'https://www.hp.com',
      'Lenovo': 'https://www.lenovo.com',
      'ASUS': 'https://www.asus.com',
      'Acer': 'https://www.acer.com',
      'MSI': 'https://www.msi.com',
      'Razer': 'https://www.razer.com',
      'Alienware': 'https://www.alienware.com',
      'Samsung': 'https://www.samsung.com',
      'LG': 'https://www.lg.com',
      'Microsoft': 'https://www.microsoft.com',
      'Google': 'https://www.google.com',
      'Framework': 'https://frame.work',
    }

    return brandWebsites[brandName] || ''
  }

  private async findOrCreateCpu(cpuSpec: any): Promise<any> {
    try {
      // Extract CPU information
      const model = cpuSpec.model || cpuSpec.name || 'Unknown CPU'
      const manufacturer = this.extractCpuManufacturer(model)

      // Find or create manufacturer
      let cpuManufacturer = await this.db.manufacturers.findFirst({
        where: { name: { equals: manufacturer, mode: 'insensitive' } },
      })

      if (!cpuManufacturer) {
        cpuManufacturer = await this.db.manufacturers.create({
          data: { name: manufacturer },
        })
      }

      // Find or create CPU
      let cpu = await this.db.cpus.findFirst({
        where: {
          model: { equals: model, mode: 'insensitive' },
          manufacturer_id: cpuManufacturer.id,
        },
      })

      if (!cpu) {
        cpu = await this.db.cpus.create({
          data: {
            model,
            manufacturer_id: cpuManufacturer.id,
            cores: cpuSpec.cores || null,
            threads: cpuSpec.threads || null,
            base_clock: cpuSpec.baseClock || null,
            boost_clock: cpuSpec.boostClock || null,
            cache_l3: cpuSpec.cache || null,
            tdp: cpuSpec.tdp || null,
            cpu_architecture_id: 1, // Default architecture
          },
        })
      }

      return cpu
    } catch (error) {
      console.error('Failed to find or create CPU:', error)
      return null
    }
  }

  private async findOrCreateGpu(gpuSpec: any): Promise<any> {
    try {
      const model = gpuSpec.model || gpuSpec.name || 'Unknown GPU'
      const manufacturer = this.extractGpuManufacturer(model)

      // Find or create manufacturer
      let gpuManufacturer = await this.db.manufacturers.findFirst({
        where: { name: { equals: manufacturer, mode: 'insensitive' } },
      })

      if (!gpuManufacturer) {
        gpuManufacturer = await this.db.manufacturers.create({
          data: { name: manufacturer },
        })
      }

      // Find or create GPU
      let gpu = await this.db.gpus.findFirst({
        where: {
          model: { equals: model, mode: 'insensitive' },
          manufacturer_id: gpuManufacturer.id,
        },
      })

      if (!gpu) {
        gpu = await this.db.gpus.create({
          data: {
            model,
            manufacturer_id: gpuManufacturer.id,
            memory_size: gpuSpec.memory || null,
            memory_type_id: 1, // Default memory type
            base_clock: gpuSpec.baseClock || null,
            boost_clock: gpuSpec.boostClock || null,
            cuda_cores: gpuSpec.cudaCores || null,
            rt_cores: gpuSpec.rtCores || null,
            tensor_cores: gpuSpec.tensorCores || null,
            tdp: gpuSpec.tdp || null,
          },
        })
      }

      return gpu
    } catch (error) {
      console.error('Failed to find or create GPU:', error)
      return null
    }
  }

  private extractCpuManufacturer(model: string): string {
    if (model.toLowerCase().includes('intel')) return 'Intel'
    if (model.toLowerCase().includes('amd')) return 'AMD'
    if (model.toLowerCase().includes('apple')) return 'Apple'
    if (model.toLowerCase().includes('qualcomm')) return 'Qualcomm'
    return 'Unknown'
  }

  private extractGpuManufacturer(model: string): string {
    if (model.toLowerCase().includes('nvidia') || model.toLowerCase().includes('rtx') || model.toLowerCase().includes('gtx')) return 'NVIDIA'
    if (model.toLowerCase().includes('amd') || model.toLowerCase().includes('radeon')) return 'AMD'
    if (model.toLowerCase().includes('intel')) return 'Intel'
    if (model.toLowerCase().includes('apple')) return 'Apple'
    return 'Unknown'
  }

  private async findOrCreateRamConfiguration(memorySpec: any): Promise<any> {
    try {
      const size = memorySpec.size || memorySpec.capacity || 8 // Default to 8GB
      const type = memorySpec.type || 'DDR4' // Default to DDR4
      const speed = memorySpec.speed || 3200 // Default speed

      // Find or create RAM configuration
      let ramConfig = await this.db.ram_configurations.findFirst({
        where: {
          capacity: size,
          type,
          speed,
        },
      })

      if (!ramConfig) {
        ramConfig = await this.db.ram_configurations.create({
          data: {
            capacity: size,
            type,
            speed,
            voltage: memorySpec.voltage || null,
            latency: memorySpec.latency || null,
          },
        })
      }

      return ramConfig
    } catch (error) {
      console.error('Failed to find or create RAM configuration:', error)
      return null
    }
  }

  private async findOrCreateStorageDevice(storageSpec: any): Promise<any> {
    try {
      const capacity = storageSpec.capacity || storageSpec.size || 256 // Default to 256GB
      const type = storageSpec.type || 'SSD' // Default to SSD
      const interface_type = storageSpec.interface || 'NVMe' // Default to NVMe

      // Find or create storage device
      let storageDevice = await this.db.storage_devices.findFirst({
        where: {
          capacity,
          type,
          interface: interface_type,
        },
      })

      if (!storageDevice) {
        storageDevice = await this.db.storage_devices.create({
          data: {
            capacity,
            type,
            interface: interface_type,
            form_factor: storageSpec.formFactor || 'M.2',
            read_speed: storageSpec.readSpeed || null,
            write_speed: storageSpec.writeSpeed || null,
          },
        })
      }

      return storageDevice
    } catch (error) {
      console.error('Failed to find or create storage device:', error)
      return null
    }
  }
}
