// Crawl4AI service for web scraping

import axios from 'axios'
import { BaseScraper, type ScrapingTarget, type ScrapedData, type ScrapingConfig } from './base-scraper'

export interface Crawl4AIConfig extends ScrapingConfig {
  apiUrl: string
  pollInterval: number
  maxPollAttempts: number
  extractionSchema?: Record<string, unknown> | undefined
  enableJavaScript: boolean
  waitForSelector?: string | undefined
  blockResources: string[]
  screenshotMode: boolean
}

export interface Crawl4AITarget extends ScrapingTarget {
  siteType?: 'amazon' | 'bestbuy' | 'newegg' | 'microcenter' | 'bhphoto' | 'adorama' | 'generic'
  extractionSchema?: Record<string, unknown>
  waitForSelector?: string
  productSelectors?: {
    title?: string
    price?: string
    specs?: string
    images?: string
    availability?: string
    reviews?: string
  }
}

export interface Crawl4AIResponse {
  task_id: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  result?: {
    url: string
    markdown?: string
    html?: string
    cleaned_html?: string
    media?: Array<{ type: string; src: string; alt?: string }>
    links?: Array<{ text: string; href: string }>
    metadata?: {
      title?: string
      description?: string
      keywords?: string[]
      language?: string
      author?: string
    }
    extracted_content?: any
    screenshot?: string
  }
  error?: string
  created_at: string
  updated_at: string
}

export class Crawl4AIService extends BaseScraper {
  private crawl4aiConfig: Crawl4AIConfig
  private axiosInstance: any

  constructor(config: Partial<Crawl4AIConfig> = {}) {
    super(config)
    
    this.crawl4aiConfig = {
      ...this.config,
      apiUrl: config.apiUrl || process.env.CRAWLAI_API_URL || 'http://localhost:11235',
      pollInterval: config.pollInterval || 3000,
      maxPollAttempts: config.maxPollAttempts || 20,
      extractionSchema: config.extractionSchema || undefined,
      enableJavaScript: config.enableJavaScript ?? true,
      waitForSelector: config.waitForSelector || undefined,
      blockResources: config.blockResources || ['image', 'stylesheet', 'font'],
      screenshotMode: config.screenshotMode ?? false,
    }

    this.axiosInstance = axios.create({
      baseURL: this.crawl4aiConfig.apiUrl,
      timeout: this.config.timeout,
      headers: {
        'Content-Type': 'application/json',
        ...this.config.headers,
      },
    })
  }

  /**
   * Scrape a single URL using Crawl4AI
   */
  async scrape(target: ScrapingTarget): Promise<ScrapedData> {
    if (!this.validateUrl(target.url)) {
      throw new Error(`Invalid URL: ${target.url}`)
    }

    try {
      await this.logActivity('info', `Starting Crawl4AI scrape`, target.url)

      // Submit crawl job
      const taskId = await this.submitCrawlJob(target)
      
      // Poll for results
      const result = await this.pollForResults(taskId)
      
      if (!result.result) {
        throw new Error(`Crawl4AI scraping failed: ${result.error || 'No result data'}`)
      }

      const content = result.result.markdown || result.result.cleaned_html || result.result.html || ''
      const metadata = this.extractMetadata(content, target.url)

      // Add Crawl4AI-specific metadata
      if (result.result.metadata) {
        Object.assign(metadata, {
          crawl4ai: {
            title: result.result.metadata.title,
            description: result.result.metadata.description,
            keywords: result.result.metadata.keywords,
            language: result.result.metadata.language,
            author: result.result.metadata.author,
          },
        })
      }

      // Add media and links if available
      if (result.result.media) {
        metadata.media = result.result.media
      }
      
      if (result.result.links) {
        metadata.links = result.result.links
      }

      // Add extracted content if schema was used
      if (result.result.extracted_content) {
        metadata.extractedContent = result.result.extracted_content
      }

      await this.logActivity('info', `Successfully scraped ${content.length} characters`, target.url)

      return {
        url: target.url,
        title: result.result.metadata?.title || 'Untitled',
        content: this.cleanText(content),
        metadata,
        timestamp: new Date(),
        success: true,
      }

    } catch (error) {
      await this.logActivity('error', `Crawl4AI scraping failed: ${error}`, target.url)
      throw error
    }
  }

  /**
   * Submit a crawl job to Crawl4AI
   */
  private async submitCrawlJob(target: ScrapingTarget): Promise<string> {
    const crawl4aiTarget = target as Crawl4AITarget
    
    const crawlRequest = {
      urls: [target.url],
      crawler_params: {
        headless: true,
        page_timeout: this.config.timeout / 1000, // Convert to seconds
        delay_before_return_html: (target.waitFor || 2000) / 1000,
        js_code: crawl4aiTarget.actions ? this.generateJavaScriptCode(crawl4aiTarget.actions) : undefined,
        wait_for: crawl4aiTarget.waitForSelector || this.crawl4aiConfig.waitForSelector,
        screenshot: this.crawl4aiConfig.screenshotMode,
        user_agent: this.config.userAgent,
      },
      extraction_config: crawl4aiTarget.extractionSchema || this.crawl4aiConfig.extractionSchema ? {
        type: 'schema',
        params: {
          schema: crawl4aiTarget.extractionSchema || this.crawl4aiConfig.extractionSchema,
        },
      } : undefined,
    }

    const response = await this.axiosInstance.post('/crawl', crawlRequest)
    
    if (!response.data?.task_id) {
      throw new Error('Failed to get task ID from Crawl4AI')
    }

    return response.data.task_id
  }

  /**
   * Poll for crawl results
   */
  private async pollForResults(taskId: string): Promise<Crawl4AIResponse> {
    let attempts = 0
    
    while (attempts < this.crawl4aiConfig.maxPollAttempts) {
      try {
        const response = await this.axiosInstance.get(`/task/${taskId}`)
        const result: Crawl4AIResponse = response.data

        if (result.status === 'completed') {
          return result
        }
        
        if (result.status === 'failed') {
          throw new Error(`Crawl4AI task failed: ${result.error || 'Unknown error'}`)
        }

        // Wait before next poll
        await new Promise(resolve => setTimeout(resolve, this.crawl4aiConfig.pollInterval))
        attempts++

      } catch (error) {
        if (axios.isAxiosError(error) && error.response?.status === 404) {
          throw new Error(`Task ${taskId} not found`)
        }
        throw error
      }
    }

    throw new Error(`Crawl4AI task ${taskId} timed out after ${attempts} attempts`)
  }

  /**
   * Generate JavaScript code from actions
   */
  private generateJavaScriptCode(actions: Array<{
    type: 'click' | 'scroll' | 'wait' | 'type'
    selector?: string
    value?: string | number
  }>): string {
    const jsCommands: string[] = []

    for (const action of actions) {
      switch (action.type) {
        case 'click':
          if (action.selector) {
            jsCommands.push(`document.querySelector('${action.selector}')?.click();`)
          }
          break
        case 'scroll':
          if (action.value) {
            jsCommands.push(`window.scrollBy(0, ${action.value});`)
          } else {
            jsCommands.push(`window.scrollTo(0, document.body.scrollHeight);`)
          }
          break
        case 'wait':
          if (action.value) {
            jsCommands.push(`await new Promise(resolve => setTimeout(resolve, ${action.value}));`)
          }
          break
        case 'type':
          if (action.selector && action.value) {
            jsCommands.push(`
              const element = document.querySelector('${action.selector}');
              if (element) {
                element.value = '${action.value}';
                element.dispatchEvent(new Event('input', { bubbles: true }));
              }
            `)
          }
          break
      }
    }

    return jsCommands.join('\n')
  }

  /**
   * Extract laptop-specific data using Crawl4AI's structured extraction
   */
  async extractLaptopData(url: string, siteType: string = 'generic'): Promise<any> {
    const laptopSchema = this.getLaptopExtractionSchema(siteType)

    const target: Crawl4AITarget = {
      url,
      siteType: siteType as any,
      extractionSchema: laptopSchema,
    }

    try {
      const result = await this.scrape(target)
      return result.metadata?.extractedContent || null
    } catch (error) {
      await this.logActivity('error', `Failed to extract laptop data: ${error}`, url)
      throw error
    }
  }

  /**
   * Get laptop extraction schema based on site type
   */
  private getLaptopExtractionSchema(siteType: string): Record<string, any> {
    const baseSchema = {
      type: 'object',
      properties: {
        title: { type: 'string', description: 'Product title or name' },
        brand: { type: 'string', description: 'Laptop brand (Dell, HP, Lenovo, etc.)' },
        model: { type: 'string', description: 'Laptop model number or name' },
        price: {
          type: 'object',
          properties: {
            current: { type: 'number', description: 'Current price' },
            original: { type: 'number', description: 'Original price if on sale' },
            currency: { type: 'string', description: 'Currency code (USD, EUR, etc.)' },
          },
        },
        specifications: {
          type: 'object',
          properties: {
            processor: { type: 'string', description: 'CPU information' },
            memory: { type: 'string', description: 'RAM amount and type' },
            storage: { type: 'string', description: 'Storage type and capacity' },
            graphics: { type: 'string', description: 'GPU information' },
            display: { type: 'string', description: 'Screen size and resolution' },
            os: { type: 'string', description: 'Operating system' },
          },
        },
        availability: {
          type: 'object',
          properties: {
            inStock: { type: 'boolean', description: 'Whether item is in stock' },
            quantity: { type: 'number', description: 'Available quantity if shown' },
            shippingInfo: { type: 'string', description: 'Shipping information' },
          },
        },
        images: {
          type: 'array',
          items: { type: 'string' },
          description: 'Product image URLs',
        },
        reviews: {
          type: 'object',
          properties: {
            rating: { type: 'number', description: 'Average rating' },
            count: { type: 'number', description: 'Number of reviews' },
            highlights: {
              type: 'array',
              items: { type: 'string' },
              description: 'Review highlights or key points',
            },
          },
        },
      },
      required: ['title'],
    }

    // Site-specific schema modifications
    switch (siteType) {
      case 'amazon':
        return {
          ...baseSchema,
          properties: {
            ...baseSchema.properties,
            asin: { type: 'string', description: 'Amazon ASIN' },
            prime: { type: 'boolean', description: 'Prime eligible' },
          },
        }
      case 'bestbuy':
        return {
          ...baseSchema,
          properties: {
            ...baseSchema.properties,
            sku: { type: 'string', description: 'Best Buy SKU' },
            geekSquad: { type: 'boolean', description: 'Geek Squad services available' },
          },
        }
      default:
        return baseSchema
    }
  }

  /**
   * Crawl a website to discover laptop product URLs
   */
  async crawlForLaptops(
    baseUrl: string,
    options: {
      maxPages?: number
      includePaths?: string[]
      excludePaths?: string[]
      laptopKeywords?: string[]
    } = {}
  ): Promise<string[]> {
    try {
      await this.logActivity('info', `Starting Crawl4AI crawl for laptop URLs`, baseUrl)

      // Use Crawl4AI to crawl and extract links
      const crawlRequest = {
        urls: [baseUrl],
        crawler_params: {
          headless: true,
          page_timeout: 30,
          max_depth: 2,
          max_pages: options.maxPages || 50,
        },
        extraction_config: {
          type: 'schema',
          params: {
            schema: {
              type: 'object',
              properties: {
                links: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      text: { type: 'string' },
                      href: { type: 'string' },
                    },
                  },
                },
              },
            },
          },
        },
      }

      const response = await this.axiosInstance.post('/crawl', crawlRequest)
      const taskId = response.data.task_id
      const result = await this.pollForResults(taskId)

      if (!result.result?.extracted_content?.links) {
        return []
      }

      // Filter links for laptop-related URLs
      const laptopKeywords = options.laptopKeywords || ['laptop', 'notebook', 'gaming', 'ultrabook', 'chromebook']
      const includePaths = options.includePaths || ['/laptop', '/notebook', '/gaming', '/computer']
      const excludePaths = options.excludePaths || ['/support', '/warranty', '/contact', '/about']

      const laptopUrls = result.result.extracted_content.links
        .filter((link: any) => {
          const href = link.href?.toLowerCase() || ''
          const text = link.text?.toLowerCase() || ''

          // Check if URL contains laptop-related paths
          const hasIncludePath = includePaths.some(path => href.includes(path.toLowerCase()))
          const hasExcludePath = excludePaths.some(path => href.includes(path.toLowerCase()))

          // Check if text contains laptop keywords
          const hasLaptopKeyword = laptopKeywords.some(keyword =>
            text.includes(keyword.toLowerCase()) || href.includes(keyword.toLowerCase())
          )

          return (hasIncludePath || hasLaptopKeyword) && !hasExcludePath
        })
        .map((link: any) => {
          // Convert relative URLs to absolute
          try {
            return new URL(link.href, baseUrl).href
          } catch {
            return null
          }
        })
        .filter((url: string | null) => url !== null)

      await this.logActivity('info', `Found ${laptopUrls.length} laptop URLs`, baseUrl)
      return laptopUrls

    } catch (error) {
      await this.logActivity('error', `Failed to crawl for laptops: ${error}`, baseUrl)
      throw error
    }
  }

  /**
   * Health check for Crawl4AI service
   */
  async healthCheck(): Promise<{ status: string; version?: string; error?: string }> {
    try {
      const response = await this.axiosInstance.get('/health', { timeout: 5000 })
      return {
        status: 'healthy',
        version: response.data?.version,
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : 'Unknown error',
      }
    }
  }
}
