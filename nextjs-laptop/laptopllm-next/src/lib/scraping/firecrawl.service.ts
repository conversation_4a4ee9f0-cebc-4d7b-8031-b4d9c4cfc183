// Firecrawl service for web scraping

import FirecrawlApp from '@mendable/firecrawl-js'
import { BaseScraper, type ScrapingTarget, type ScrapedData, type ScrapingConfig } from './base-scraper'

export interface FirecrawlConfig extends ScrapingConfig {
  apiKey: string
  formats: Array<'markdown' | 'html' | 'rawHtml' | 'screenshot' | 'links'>
  onlyMainContent: boolean
  includeTags: string[]
  excludeTags: string[]
  waitFor: number
}

export interface LaptopScrapingTarget extends ScrapingTarget {
  siteType: 'amazon' | 'bestbuy' | 'newegg' | 'microcenter' | 'bhphoto' | 'adorama' | 'generic'
  productSelectors?: {
    title?: string
    price?: string
    specs?: string
    images?: string
    availability?: string
    reviews?: string
  }
}

export interface LaptopData {
  title: string
  brand?: string
  model?: string
  price?: {
    current: number
    original?: number
    currency: string
  }
  specifications?: Record<string, any>
  images?: string[]
  availability?: {
    inStock: boolean
    quantity?: number
    shippingInfo?: string
  }
  reviews?: {
    rating: number
    count: number
    highlights?: string[]
  }
  url: string
  source: string
  scrapedAt: Date
}

export class FirecrawlService extends BaseScraper {
  private firecrawl: FirecrawlApp
  private firecrawlConfig: FirecrawlConfig

  constructor(config: Partial<FirecrawlConfig> = {}) {
    super(config)
    
    this.firecrawlConfig = {
      ...this.config,
      apiKey: config.apiKey || process.env.FIRECRAWL_API_KEY || '',
      formats: config.formats || ['markdown'],
      onlyMainContent: config.onlyMainContent ?? true,
      includeTags: config.includeTags || [],
      excludeTags: config.excludeTags || ['nav', 'footer', 'aside', 'script', 'style'],
      waitFor: config.waitFor || 3000,
    }

    if (!this.firecrawlConfig.apiKey) {
      throw new Error('Firecrawl API key is required')
    }

    this.firecrawl = new FirecrawlApp({ apiKey: this.firecrawlConfig.apiKey })
  }

  /**
   * Scrape a single URL using Firecrawl
   */
  async scrape(target: ScrapingTarget): Promise<ScrapedData> {
    if (!this.validateUrl(target.url)) {
      throw new Error(`Invalid URL: ${target.url}`)
    }

    try {
      await this.logActivity('info', `Starting Firecrawl scrape`, target.url)

      const scrapeOptions = {
        formats: this.firecrawlConfig.formats,
        onlyMainContent: this.firecrawlConfig.onlyMainContent,
        includeTags: this.firecrawlConfig.includeTags,
        excludeTags: this.firecrawlConfig.excludeTags,
        waitFor: target.waitFor || this.firecrawlConfig.waitFor,
        timeout: this.config.timeout,
      }

      const result = await this.firecrawl.scrapeUrl(target.url, scrapeOptions)

      if (!result.success) {
        throw new Error(`Firecrawl scraping failed: ${result.error || 'Unknown error'}`)
      }

      const content = result.data?.markdown || result.data?.html || ''
      const metadata = this.extractMetadata(content, target.url)

      // Add Firecrawl-specific metadata
      if (result.data?.metadata) {
        Object.assign(metadata, {
          firecrawl: {
            title: result.data.metadata.title,
            description: result.data.metadata.description,
            language: result.data.metadata.language,
            sourceURL: result.data.metadata.sourceURL,
            statusCode: result.data.metadata.statusCode,
          },
        })
      }

      await this.logActivity('info', `Successfully scraped ${content.length} characters`, target.url)

      return {
        url: target.url,
        title: result.data?.metadata?.title,
        content: this.cleanText(content),
        metadata,
        timestamp: new Date(),
        success: true,
      }
    } catch (error) {
      await this.logActivity('error', `Firecrawl scraping failed: ${error}`, target.url)
      throw error
    }
  }

  /**
   * Scrape laptop product pages with structured data extraction
   */
  async scrapeLaptopProducts(targets: LaptopScrapingTarget[]): Promise<LaptopData[]> {
    const results: LaptopData[] = []

    for (const target of targets) {
      try {
        const laptopData = await this.scrapeLaptopProduct(target)
        results.push(laptopData)

        // Rate limiting
        if (this.config.rateLimit > 0) {
          await new Promise(resolve => setTimeout(resolve, this.config.rateLimit))
        }
      } catch (error) {
        console.error(`Failed to scrape laptop from ${target.url}:`, error)
        await this.logActivity('error', `Failed to scrape laptop: ${error}`, target.url)
      }
    }

    return results
  }

  /**
   * Scrape a single laptop product page
   */
  async scrapeLaptopProduct(target: LaptopScrapingTarget): Promise<LaptopData> {
    const scrapedData = await this.scrape(target)
    
    if (!scrapedData.success) {
      throw new Error(`Failed to scrape ${target.url}`)
    }

    // Extract structured laptop data based on site type
    const laptopData = await this.extractLaptopData(scrapedData, target)
    
    return laptopData
  }

  /**
   * Crawl a website to discover laptop product URLs
   */
  async crawlForLaptops(
    baseUrl: string,
    options: {
      maxPages?: number
      includePaths?: string[]
      excludePaths?: string[]
      laptopKeywords?: string[]
    } = {}
  ): Promise<string[]> {
    try {
      await this.logActivity('info', `Starting crawl for laptop URLs`, baseUrl)

      const crawlOptions = {
        limit: options.maxPages || 50,
        includePaths: options.includePaths || ['/laptop', '/notebook', '/gaming', '/computer'],
        excludePaths: options.excludePaths || ['/support', '/warranty', '/contact', '/about'],
        allowExternalLinks: false,
        maxDepth: 3,
      }

      const crawlResult = await this.firecrawl.crawlUrl(baseUrl, {
        ...crawlOptions,
        scrapeOptions: {
          formats: ['links'],
          onlyMainContent: true,
        },
      })

      if (!crawlResult.success) {
        throw new Error(`Crawling failed: ${crawlResult.error || 'Unknown error'}`)
      }

      // Filter URLs that likely contain laptop products
      const laptopKeywords = options.laptopKeywords || [
        'laptop', 'notebook', 'gaming', 'ultrabook', 'chromebook',
        'macbook', 'thinkpad', 'inspiron', 'pavilion', 'aspire'
      ]

      const laptopUrls = crawlResult.data
        ?.map(item => item.metadata?.sourceURL)
        .filter((url): url is string => {
          if (!url) return false
          const urlLower = url.toLowerCase()
          return laptopKeywords.some(keyword => urlLower.includes(keyword))
        }) || []

      await this.logActivity('info', `Found ${laptopUrls.length} potential laptop URLs`, baseUrl)

      return [...new Set(laptopUrls)] // Remove duplicates
    } catch (error) {
      await this.logActivity('error', `Crawling failed: ${error}`, baseUrl)
      throw error
    }
  }

  /**
   * Search for laptops using Firecrawl's search functionality
   */
  async searchLaptops(
    query: string,
    options: {
      limit?: number
      country?: string
      language?: string
      includeDomains?: string[]
    } = {}
  ): Promise<LaptopData[]> {
    try {
      await this.logActivity('info', `Searching for laptops: ${query}`)

      const searchOptions = {
        limit: options.limit || 10,
        country: options.country || 'us',
        lang: options.language || 'en',
        scrapeOptions: {
          formats: ['markdown'] as const,
          onlyMainContent: true,
          waitFor: 3000,
        },
      }

      const searchResult = await this.firecrawl.search(query, searchOptions)

      if (!searchResult.success) {
        throw new Error(`Search failed: ${searchResult.error || 'Unknown error'}`)
      }

      const laptopData: LaptopData[] = []

      for (const result of searchResult.data || []) {
        try {
          if (result.metadata?.sourceURL) {
            const target: LaptopScrapingTarget = {
              url: result.metadata.sourceURL,
              siteType: this.detectSiteType(result.metadata.sourceURL),
            }

            const scrapedData: ScrapedData = {
              url: result.metadata.sourceURL,
              title: result.metadata.title,
              content: result.markdown || '',
              metadata: result.metadata,
              timestamp: new Date(),
              success: true,
            }

            const laptop = await this.extractLaptopData(scrapedData, target)
            laptopData.push(laptop)
          }
        } catch (error) {
          console.error(`Failed to process search result:`, error)
        }
      }

      await this.logActivity('info', `Search completed: found ${laptopData.length} laptops`)

      return laptopData
    } catch (error) {
      await this.logActivity('error', `Search failed: ${error}`)
      throw error
    }
  }

  /**
   * Extract structured laptop data from scraped content
   */
  private async extractLaptopData(
    scrapedData: ScrapedData,
    target: LaptopScrapingTarget
  ): Promise<LaptopData> {
    const content = scrapedData.content
    const url = scrapedData.url

    // Use Firecrawl's extract feature for structured data
    try {
      const extractResult = await this.firecrawl.scrapeUrl(url, {
        formats: ['extract'],
        extract: {
          schema: {
            type: 'object',
            properties: {
              title: { type: 'string' },
              brand: { type: 'string' },
              model: { type: 'string' },
              price: {
                type: 'object',
                properties: {
                  current: { type: 'number' },
                  original: { type: 'number' },
                  currency: { type: 'string' },
                },
              },
              specifications: { type: 'object' },
              availability: {
                type: 'object',
                properties: {
                  inStock: { type: 'boolean' },
                  quantity: { type: 'number' },
                  shippingInfo: { type: 'string' },
                },
              },
              reviews: {
                type: 'object',
                properties: {
                  rating: { type: 'number' },
                  count: { type: 'number' },
                  highlights: { type: 'array', items: { type: 'string' } },
                },
              },
              images: { type: 'array', items: { type: 'string' } },
            },
          },
          systemPrompt: 'Extract laptop product information from this e-commerce page.',
          prompt: 'Extract the laptop title, brand, model, current price, specifications, availability, reviews, and product images.',
        },
      })

      if (extractResult.success && extractResult.data?.extract) {
        const extracted = extractResult.data.extract as any

        return {
          title: extracted.title || scrapedData.title || 'Unknown Laptop',
          brand: extracted.brand,
          model: extracted.model,
          price: extracted.price,
          specifications: extracted.specifications || {},
          images: extracted.images || [],
          availability: extracted.availability,
          reviews: extracted.reviews,
          url,
          source: this.extractDomain(url),
          scrapedAt: new Date(),
        }
      }
    } catch (error) {
      console.warn('Structured extraction failed, falling back to text parsing:', error)
    }

    // Fallback to text-based extraction
    return this.extractLaptopDataFromText(content, url, target.siteType)
  }

  /**
   * Extract laptop data from text content (fallback method)
   */
  private extractLaptopDataFromText(
    content: string,
    url: string,
    siteType: LaptopScrapingTarget['siteType']
  ): LaptopData {
    const laptopData: LaptopData = {
      title: 'Unknown Laptop',
      url,
      source: this.extractDomain(url),
      scrapedAt: new Date(),
    }

    // Extract title - prioritize markdown headers, then first meaningful line
    const titleMatch = content.match(/^#\s*(.+)$/m)
    if (titleMatch) {
      laptopData.title = this.cleanText(titleMatch[1])
    } else {
      // Look for first line that might be a title (not just any first line)
      const lines = content.split('\n').map(line => line.trim()).filter(line => line.length > 0)
      const firstLine = lines[0]
      if (firstLine && firstLine.length > 5 && firstLine.length < 100) {
        laptopData.title = this.cleanText(firstLine)
      }
    }

    // Extract price (basic patterns)
    const pricePatterns = [
      /\$[\d,]+\.?\d*/g,
      /USD\s*[\d,]+\.?\d*/g,
      /Price:\s*\$?([\d,]+\.?\d*)/gi,
    ]

    for (const pattern of pricePatterns) {
      const priceMatch = content.match(pattern)
      if (priceMatch) {
        const priceStr = priceMatch[0].replace(/[^\d.]/g, '')
        const price = parseFloat(priceStr)
        if (!isNaN(price)) {
          laptopData.price = {
            current: price,
            currency: 'USD',
          }
          break
        }
      }
    }

    // Extract brand from title or content
    const brands = ['Apple', 'Dell', 'HP', 'Lenovo', 'ASUS', 'Acer', 'MSI', 'Razer', 'Alienware', 'Surface']
    for (const brand of brands) {
      if (laptopData.title.toLowerCase().includes(brand.toLowerCase())) {
        laptopData.brand = brand
        break
      }
    }

    // Extract basic specifications
    const specs: Record<string, any> = {}
    
    // RAM
    const ramMatch = content.match(/(\d+)\s*GB\s*(RAM|Memory)/i)
    if (ramMatch) {
      specs.ram = `${ramMatch[1]}GB`
    }

    // Storage
    const storageMatch = content.match(/(\d+)\s*(GB|TB)\s*(SSD|HDD|Storage)/i)
    if (storageMatch) {
      specs.storage = `${storageMatch[1]}${storageMatch[2]} ${storageMatch[3]}`
    }

    // CPU
    const cpuMatch = content.match(/(Intel|AMD)\s+([^,\n]+)/i)
    if (cpuMatch) {
      specs.cpu = this.cleanText(cpuMatch[0])
    }

    if (Object.keys(specs).length > 0) {
      laptopData.specifications = specs
    }

    return laptopData
  }

  /**
   * Detect site type from URL
   */
  private detectSiteType(url: string): LaptopScrapingTarget['siteType'] {
    const domain = this.extractDomain(url).toLowerCase()

    if (domain.includes('amazon')) return 'amazon'
    if (domain.includes('bestbuy')) return 'bestbuy'
    if (domain.includes('newegg')) return 'newegg'
    if (domain.includes('microcenter')) return 'microcenter'
    if (domain.includes('bhphoto')) return 'bhphoto'
    if (domain.includes('adorama')) return 'adorama'

    return 'generic'
  }
}
