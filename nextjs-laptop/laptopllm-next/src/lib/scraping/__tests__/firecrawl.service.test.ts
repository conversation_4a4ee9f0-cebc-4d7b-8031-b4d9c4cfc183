// Tests for FirecrawlService

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { FirecrawlService, type LaptopScrapingTarget } from '../firecrawl.service'

// Mock FirecrawlApp
vi.mock('@mendable/firecrawl-js', () => ({
  default: vi.fn().mockImplementation(() => ({
    scrapeUrl: vi.fn(),
    crawlUrl: vi.fn(),
    search: vi.fn(),
  })),
}))

// Mock Prisma
vi.mock('../../prisma', () => ({
  prisma: {
    scrapingJob: {
      findUnique: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
    },
    scrapingLog: {
      create: vi.fn(),
    },
  },
}))

describe('FirecrawlService', () => {
  let service: FirecrawlService
  let mockFirecrawl: any

  beforeEach(() => {
    // Set up environment variable
    process.env.FIRECRAWL_API_KEY = 'test-api-key'
    
    service = new FirecrawlService()
    mockFirecrawl = (service as any).firecrawl
    vi.clearAllMocks()
  })

  afterEach(() => {
    delete process.env.FIRECRAWL_API_KEY
  })

  describe('Initialization', () => {
    it('should initialize with API key from environment', () => {
      expect(service).toBeDefined()
      expect((service as any).firecrawlConfig.apiKey).toBe('test-api-key')
    })

    it('should throw error without API key', () => {
      delete process.env.FIRECRAWL_API_KEY
      
      expect(() => new FirecrawlService()).toThrow('Firecrawl API key is required')
    })

    it('should accept custom configuration', () => {
      const customService = new FirecrawlService({
        apiKey: 'custom-key',
        formats: ['html'],
        onlyMainContent: false,
        waitFor: 5000,
      })

      const config = (customService as any).firecrawlConfig
      expect(config.apiKey).toBe('custom-key')
      expect(config.formats).toEqual(['html'])
      expect(config.onlyMainContent).toBe(false)
      expect(config.waitFor).toBe(5000)
    })
  })

  describe('Basic scraping', () => {
    it('should scrape a URL successfully', async () => {
      const mockResponse = {
        success: true,
        data: {
          markdown: '# Test Page\nThis is test content',
          metadata: {
            title: 'Test Page',
            description: 'Test description',
            sourceURL: 'https://example.com',
            statusCode: 200,
          },
        },
      }

      mockFirecrawl.scrapeUrl.mockResolvedValue(mockResponse)

      const target = { url: 'https://example.com' }
      const result = await service.scrape(target)

      expect(result.success).toBe(true)
      expect(result.url).toBe('https://example.com')
      expect(result.title).toBe('Test Page')
      expect(result.content).toContain('Test Page')
      expect(result.metadata.firecrawl.title).toBe('Test Page')
      expect(mockFirecrawl.scrapeUrl).toHaveBeenCalledWith(
        'https://example.com',
        expect.objectContaining({
          formats: ['markdown'],
          onlyMainContent: true,
        })
      )
    })

    it('should handle scraping failures', async () => {
      const mockResponse = {
        success: false,
        error: 'Failed to scrape',
      }

      mockFirecrawl.scrapeUrl.mockResolvedValue(mockResponse)

      const target = { url: 'https://example.com' }

      await expect(service.scrape(target)).rejects.toThrow('Firecrawl scraping failed: Failed to scrape')
    })

    it('should reject invalid URLs', async () => {
      const target = { url: 'invalid-url' }

      await expect(service.scrape(target)).rejects.toThrow('Invalid URL: invalid-url')
    })
  })

  describe('Laptop product scraping', () => {
    it('should scrape laptop product with structured extraction', async () => {
      const mockScrapeResponse = {
        success: true,
        data: {
          markdown: '# Gaming Laptop\nPrice: $1299\nSpecs: 16GB RAM, RTX 3060',
          metadata: {
            title: 'Gaming Laptop',
            sourceURL: 'https://example.com/laptop',
          },
        },
      }

      const mockExtractResponse = {
        success: true,
        data: {
          extract: {
            title: 'Gaming Laptop RTX 3060',
            brand: 'ASUS',
            model: 'ROG Strix',
            price: {
              current: 1299,
              currency: 'USD',
            },
            specifications: {
              ram: '16GB',
              gpu: 'RTX 3060',
            },
            availability: {
              inStock: true,
            },
            images: ['https://example.com/image1.jpg'],
          },
        },
      }

      mockFirecrawl.scrapeUrl
        .mockResolvedValueOnce(mockScrapeResponse)
        .mockResolvedValueOnce(mockExtractResponse)

      const target: LaptopScrapingTarget = {
        url: 'https://example.com/laptop',
        siteType: 'generic',
      }

      const result = await service.scrapeLaptopProduct(target)

      expect(result.title).toBe('Gaming Laptop RTX 3060')
      expect(result.brand).toBe('ASUS')
      expect(result.model).toBe('ROG Strix')
      expect(result.price?.current).toBe(1299)
      expect(result.price?.currency).toBe('USD')
      expect(result.specifications?.ram).toBe('16GB')
      expect(result.specifications?.gpu).toBe('RTX 3060')
      expect(result.availability?.inStock).toBe(true)
      expect(result.images).toEqual(['https://example.com/image1.jpg'])
      expect(result.url).toBe('https://example.com/laptop')
      expect(result.source).toBe('example.com')
    })

    it('should fallback to text parsing when structured extraction fails', async () => {
      const mockScrapeResponse = {
        success: true,
        data: {
          markdown: '# Dell Inspiron Laptop\nPrice: $899\nRAM: 8GB\nCPU: Intel i5',
          metadata: {
            title: 'Dell Inspiron Laptop',
            sourceURL: 'https://example.com/laptop',
          },
        },
      }

      const mockExtractResponse = {
        success: false,
        error: 'Extraction failed',
      }

      mockFirecrawl.scrapeUrl
        .mockResolvedValueOnce(mockScrapeResponse)
        .mockResolvedValueOnce(mockExtractResponse)

      const target: LaptopScrapingTarget = {
        url: 'https://example.com/laptop',
        siteType: 'generic',
      }

      const result = await service.scrapeLaptopProduct(target)

      expect(result.title).toContain('Dell Inspiron Laptop')
      expect(result.brand).toBe('Dell')
      expect(result.price?.current).toBe(899)
      expect(result.url).toBe('https://example.com/laptop')
      expect(result.source).toBe('example.com')
    })

    it('should scrape multiple laptop products', async () => {
      const mockResponse = {
        success: true,
        data: {
          markdown: '# Test Laptop\nPrice: $999',
          metadata: {
            title: 'Test Laptop',
            sourceURL: 'https://example.com/laptop1',
          },
        },
      }

      mockFirecrawl.scrapeUrl.mockResolvedValue(mockResponse)

      const targets: LaptopScrapingTarget[] = [
        { url: 'https://example.com/laptop1', siteType: 'generic' },
        { url: 'https://example.com/laptop2', siteType: 'generic' },
      ]

      const results = await service.scrapeLaptopProducts(targets)

      expect(results).toHaveLength(2)
      expect(results[0].title).toContain('Test Laptop')
      expect(results[1].title).toContain('Test Laptop')
    })
  })

  describe('Site type detection', () => {
    it('should detect Amazon site type', () => {
      const siteType = (service as any).detectSiteType('https://amazon.com/laptop')
      expect(siteType).toBe('amazon')
    })

    it('should detect Best Buy site type', () => {
      const siteType = (service as any).detectSiteType('https://bestbuy.com/laptop')
      expect(siteType).toBe('bestbuy')
    })

    it('should detect Newegg site type', () => {
      const siteType = (service as any).detectSiteType('https://newegg.com/laptop')
      expect(siteType).toBe('newegg')
    })

    it('should default to generic for unknown sites', () => {
      const siteType = (service as any).detectSiteType('https://unknown-site.com/laptop')
      expect(siteType).toBe('generic')
    })
  })

  describe('Crawling for laptops', () => {
    it('should crawl website for laptop URLs', async () => {
      const mockCrawlResponse = {
        success: true,
        data: [
          { metadata: { sourceURL: 'https://example.com/laptop1' } },
          { metadata: { sourceURL: 'https://example.com/laptop2' } },
          { metadata: { sourceURL: 'https://example.com/desktop' } }, // Should be filtered out
        ],
      }

      mockFirecrawl.crawlUrl.mockResolvedValue(mockCrawlResponse)

      const urls = await service.crawlForLaptops('https://example.com')

      expect(urls).toHaveLength(2)
      expect(urls).toContain('https://example.com/laptop1')
      expect(urls).toContain('https://example.com/laptop2')
      expect(urls).not.toContain('https://example.com/desktop')
    })

    it('should handle crawling failures', async () => {
      const mockCrawlResponse = {
        success: false,
        error: 'Crawling failed',
      }

      mockFirecrawl.crawlUrl.mockResolvedValue(mockCrawlResponse)

      await expect(service.crawlForLaptops('https://example.com')).rejects.toThrow('Crawling failed')
    })
  })

  describe('Search functionality', () => {
    it('should search for laptops', async () => {
      const mockSearchResponse = {
        success: true,
        data: [
          {
            markdown: '# Gaming Laptop\nPrice: $1299',
            metadata: {
              title: 'Gaming Laptop',
              sourceURL: 'https://example.com/laptop1',
            },
          },
          {
            markdown: '# Business Laptop\nPrice: $899',
            metadata: {
              title: 'Business Laptop',
              sourceURL: 'https://example.com/laptop2',
            },
          },
        ],
      }

      mockFirecrawl.search.mockResolvedValue(mockSearchResponse)

      const results = await service.searchLaptops('gaming laptop')

      expect(results).toHaveLength(2)
      expect(results[0].title).toBe('Gaming Laptop')
      expect(results[1].title).toBe('Business Laptop')
      expect(mockFirecrawl.search).toHaveBeenCalledWith(
        'gaming laptop',
        expect.objectContaining({
          limit: 10,
          country: 'us',
          lang: 'en',
        })
      )
    })

    it('should handle search failures', async () => {
      const mockSearchResponse = {
        success: false,
        error: 'Search failed',
      }

      mockFirecrawl.search.mockResolvedValue(mockSearchResponse)

      await expect(service.searchLaptops('gaming laptop')).rejects.toThrow('Search failed')
    })
  })

  describe('Text parsing fallback', () => {
    it('should extract laptop data from text content', () => {
      const content = `# Dell XPS 13 Laptop
Price: $1,299.99
Specifications:
- 16GB RAM
- 512GB SSD
- Intel Core i7
- Intel Iris Xe Graphics`

      const result = (service as any).extractLaptopDataFromText(
        content,
        'https://example.com/laptop',
        'generic'
      )

      expect(result.title).toBe('Dell XPS 13 Laptop')
      expect(result.brand).toBe('Dell')
      expect(result.price?.current).toBe(1299.99)
      expect(result.url).toBe('https://example.com/laptop')
      expect(result.source).toBe('example.com')
    })

    it('should handle content without clear structure', () => {
      const content = 'Some random text without laptop information'

      const result = (service as any).extractLaptopDataFromText(
        content,
        'https://example.com/page',
        'generic'
      )

      // Should use the first line as title if it's reasonable length
      expect(result.title).toBe('Some random text without laptop information')
      expect(result.url).toBe('https://example.com/page')
      expect(result.source).toBe('example.com')
      expect(result.brand).toBeUndefined()
      expect(result.price).toBeUndefined()
    })
  })
})
