// Tests for BaseScraper

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { BaseScraper, type ScrapingTarget, type ScrapedData } from '../base-scraper'

// Mock implementation of BaseScraper for testing
class TestScraper extends BaseScraper {
  async scrape(target: ScrapingTarget): Promise<ScrapedData> {
    if (!this.validateUrl(target.url)) {
      throw new Error(`Invalid URL: ${target.url}`)
    }

    return {
      url: target.url,
      title: 'Test Page',
      content: 'Test content from ' + target.url,
      metadata: this.extractMetadata('Test content', target.url),
      timestamp: new Date(),
      success: true,
    }
  }
}

// Mock Prisma
vi.mock('../../prisma', () => ({
  prisma: {
    scrapingJob: {
      findUnique: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
    },
    scrapingLog: {
      create: vi.fn(),
    },
  },
}))

describe('BaseScraper', () => {
  let scraper: TestScraper

  beforeEach(() => {
    scraper = new TestScraper()
    vi.clearAllMocks()
  })

  describe('URL validation', () => {
    it('should validate HTTP URLs', () => {
      expect(scraper['validateUrl']('http://example.com')).toBe(true)
      expect(scraper['validateUrl']('https://example.com')).toBe(true)
    })

    it('should reject invalid URLs', () => {
      expect(scraper['validateUrl']('ftp://example.com')).toBe(false)
      expect(scraper['validateUrl']('invalid-url')).toBe(false)
      expect(scraper['validateUrl']('')).toBe(false)
    })
  })

  describe('Domain extraction', () => {
    it('should extract domain from URL', () => {
      expect(scraper['extractDomain']('https://example.com/path')).toBe('example.com')
      expect(scraper['extractDomain']('http://subdomain.example.com')).toBe('subdomain.example.com')
    })

    it('should handle invalid URLs', () => {
      expect(scraper['extractDomain']('invalid-url')).toBe('unknown')
    })
  })

  describe('Text cleaning', () => {
    it('should clean and normalize text', () => {
      const dirtyText = '  Multiple   spaces  \n\n\n  and   newlines  '
      const cleanText = scraper['cleanText'](dirtyText)
      expect(cleanText).toBe('Multiple spaces and newlines')
    })

    it('should handle empty text', () => {
      expect(scraper['cleanText']('')).toBe('')
      expect(scraper['cleanText']('   ')).toBe('')
    })
  })

  describe('Metadata extraction', () => {
    it('should extract basic metadata', () => {
      const content = '<title>Test Page</title><meta name="description" content="Test description">'
      const metadata = scraper['extractMetadata'](content, 'https://example.com')

      expect(metadata.domain).toBe('example.com')
      expect(metadata.contentLength).toBe(content.length)
      expect(metadata.wordCount).toBeGreaterThan(0)
      expect(metadata.title).toBe('Test Page')
      expect(metadata.description).toBe('Test description')
    })

    it('should handle content without metadata', () => {
      const content = 'Plain text content'
      const metadata = scraper['extractMetadata'](content, 'https://example.com')

      expect(metadata.domain).toBe('example.com')
      expect(metadata.contentLength).toBe(content.length)
      expect(metadata.title).toBeUndefined()
      expect(metadata.description).toBeUndefined()
    })
  })

  describe('Single scraping', () => {
    it('should scrape a valid URL', async () => {
      const target: ScrapingTarget = {
        url: 'https://example.com',
      }

      const result = await scraper.scrape(target)

      expect(result.success).toBe(true)
      expect(result.url).toBe(target.url)
      expect(result.content).toContain('Test content')
      expect(result.metadata.domain).toBe('example.com')
    })

    it('should reject invalid URLs', async () => {
      const target: ScrapingTarget = {
        url: 'invalid-url',
      }

      await expect(scraper.scrape(target)).rejects.toThrow('Invalid URL')
    })
  })

  describe('Multiple scraping', () => {
    it('should scrape multiple URLs', async () => {
      const targets: ScrapingTarget[] = [
        { url: 'https://example1.com' },
        { url: 'https://example2.com' },
        { url: 'https://example3.com' },
      ]

      const results = await scraper.scrapeMultiple(targets)

      expect(results).toHaveLength(3)
      expect(results.every(r => r.success)).toBe(true)
      expect(results[0].url).toBe('https://example1.com')
      expect(results[1].url).toBe('https://example2.com')
      expect(results[2].url).toBe('https://example3.com')
    })

    it('should handle mixed success and failure', async () => {
      const targets: ScrapingTarget[] = [
        { url: 'https://example.com' },
        { url: 'invalid-url' },
        { url: 'https://another-example.com' },
      ]

      const results = await scraper.scrapeMultiple(targets)

      expect(results).toHaveLength(3)
      expect(results[0].success).toBe(true)
      expect(results[1].success).toBe(false)
      expect(results[1].error).toContain('Invalid URL')
      expect(results[2].success).toBe(true)
    }, 10000) // Increase timeout to 10 seconds
  })

  describe('Retry logic', () => {
    it('should retry failed scraping attempts', async () => {
      let attemptCount = 0
      const failingScraper = new (class extends BaseScraper {
        async scrape(target: ScrapingTarget): Promise<ScrapedData> {
          attemptCount++
          if (attemptCount < 3) {
            throw new Error('Temporary failure')
          }
          return {
            url: target.url,
            content: 'Success after retries',
            metadata: {},
            timestamp: new Date(),
            success: true,
          }
        }
      })()

      const target: ScrapingTarget = { url: 'https://example.com' }
      const result = await failingScraper['scrapeWithRetry'](target)

      expect(result.success).toBe(true)
      expect(result.content).toBe('Success after retries')
      expect(attemptCount).toBe(3)
    })

    it('should fail after max retries', async () => {
      const failingScraper = new (class extends BaseScraper {
        async scrape(): Promise<ScrapedData> {
          throw new Error('Persistent failure')
        }
      })()

      const target: ScrapingTarget = { url: 'https://example.com' }

      await expect(failingScraper['scrapeWithRetry'](target)).rejects.toThrow('Persistent failure')
    })
  })

  describe('Job management', () => {
    it('should start a scraping job', async () => {
      const mockDb = scraper['db'] as any
      mockDb.scrapingJob.create.mockResolvedValue({ id: 'test-job' })

      const targets: ScrapingTarget[] = [
        { url: 'https://example.com' },
      ]

      const jobId = await scraper.startJob(targets, 'test-job')

      expect(jobId).toBeDefined()
      expect(mockDb.scrapingJob.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          id: jobId,
          job_type: 'test-job',
          status: 'running',
          total_urls: 1,
        }),
      })
    })

    it('should prevent starting multiple jobs', async () => {
      const mockDb = scraper['db'] as any
      mockDb.scrapingJob.create.mockResolvedValue({ id: 'test-job' })

      const targets: ScrapingTarget[] = [{ url: 'https://example.com' }]

      await scraper.startJob(targets)
      
      await expect(scraper.startJob(targets)).rejects.toThrow('Scraper is already running')
    })

    it('should get job status', async () => {
      const mockJob = {
        id: 'test-job',
        status: 'completed',
        completed_urls: 5,
        total_urls: 10,
        failed_urls: 1,
        started_at: new Date(),
        completed_at: new Date(),
        error_message: null,
        scrapingLog: [],
      }

      const mockDb = scraper['db'] as any
      mockDb.scrapingJob.findUnique.mockResolvedValue(mockJob)

      const status = await scraper.getJobStatus('test-job')

      expect(status.id).toBe('test-job')
      expect(status.status).toBe('completed')
      expect(status.progress.completed).toBe(5)
      expect(status.progress.total).toBe(10)
      expect(status.progress.failed).toBe(1)
    })

    it('should cancel a job', async () => {
      const mockDb = scraper['db'] as any
      mockDb.scrapingJob.update.mockResolvedValue({})

      await scraper.cancelJob('test-job')

      expect(mockDb.scrapingJob.update).toHaveBeenCalledWith({
        where: { id: 'test-job' },
        data: {
          status: 'cancelled',
          completed_at: expect.any(Date),
        },
      })
    })
  })

  describe('Rate limiting', () => {
    it('should respect rate limiting configuration', async () => {
      const rateLimitedScraper = new TestScraper({
        rateLimit: 100, // 100ms delay
      })

      const targets: ScrapingTarget[] = [
        { url: 'https://example1.com' },
        { url: 'https://example2.com' },
      ]

      const startTime = Date.now()
      await rateLimitedScraper.scrapeMultiple(targets)
      const endTime = Date.now()

      // Should take at least 100ms due to rate limiting
      expect(endTime - startTime).toBeGreaterThan(100)
    })
  })

  describe('Configuration', () => {
    it('should use default configuration', () => {
      const defaultScraper = new TestScraper()
      
      expect(defaultScraper['config'].maxRetries).toBe(3)
      expect(defaultScraper['config'].retryDelay).toBe(1000)
      expect(defaultScraper['config'].timeout).toBe(30000)
      expect(defaultScraper['config'].rateLimit).toBe(1000)
    })

    it('should accept custom configuration', () => {
      const customScraper = new TestScraper({
        maxRetries: 5,
        retryDelay: 2000,
        timeout: 60000,
        rateLimit: 500,
      })

      expect(customScraper['config'].maxRetries).toBe(5)
      expect(customScraper['config'].retryDelay).toBe(2000)
      expect(customScraper['config'].timeout).toBe(60000)
      expect(customScraper['config'].rateLimit).toBe(500)
    })
  })
})
