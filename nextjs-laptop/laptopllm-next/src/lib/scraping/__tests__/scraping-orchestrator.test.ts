// Tests for ScrapingOrchestrator

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { ScrapingOrchestrator, type ScrapingStrategy } from '../scraping-orchestrator'
import type { LaptopScrapingTarget, LaptopData } from '../firecrawl.service'

// Mock the services
vi.mock('../firecrawl.service', () => ({
  FirecrawlService: vi.fn().mockImplementation(() => ({
    scrapeLaptopProduct: vi.fn(),
    crawlForLaptops: vi.fn(),
    searchLaptops: vi.fn(),
  })),
}))

vi.mock('../puppeteer.service', () => ({
  PuppeteerService: vi.fn().mockImplementation(() => ({
    scrapeLaptopProduct: vi.fn(),
  })),
}))

// Mock Prisma
vi.mock('../../prisma', () => ({
  prisma: {
    laptops: {
      findFirst: vi.fn(),
    },
    laptop_listings: {
      upsert: vi.fn(),
    },
  },
}))

describe('ScrapingOrchestrator', () => {
  let orchestrator: ScrapingOrchestrator
  let mockFirecrawlService: any
  let mockPuppeteerService: any

  beforeEach(() => {
    orchestrator = new ScrapingOrchestrator()
    mockFirecrawlService = (orchestrator as any).firecrawlService
    mockPuppeteerService = (orchestrator as any).puppeteerService
    vi.clearAllMocks()
  })

  afterEach(() => {
    // Clean up any active jobs
    orchestrator.cleanupCompletedJobs(0)
  })

  describe('Initialization', () => {
    it('should initialize with default services', () => {
      expect(orchestrator).toBeDefined()
      expect(mockFirecrawlService).toBeDefined()
      expect(mockPuppeteerService).toBeDefined()
    })
  })

  describe('Single laptop scraping with fallback', () => {
    const mockLaptopData: LaptopData = {
      title: 'Test Gaming Laptop',
      brand: 'ASUS',
      model: 'ROG Strix',
      price: { current: 1299, currency: 'USD' },
      url: 'https://example.com/laptop',
      source: 'example.com',
      scrapedAt: new Date(),
    }

    it('should use primary strategy (Firecrawl) successfully', async () => {
      mockFirecrawlService.scrapeLaptopProduct.mockResolvedValue(mockLaptopData)

      const target: LaptopScrapingTarget = {
        url: 'https://example.com/laptop',
        siteType: 'generic',
      }

      const strategy: ScrapingStrategy = {
        primary: 'firecrawl',
        fallback: 'puppeteer',
        retryWithFallback: true,
      }

      const result = await orchestrator.scrapeLaptopWithFallback(target, strategy)

      expect(result).toEqual(mockLaptopData)
      expect(mockFirecrawlService.scrapeLaptopProduct).toHaveBeenCalledWith(target)
      expect(mockPuppeteerService.scrapeLaptopProduct).not.toHaveBeenCalled()
    })

    it('should fallback to Puppeteer when Firecrawl fails', async () => {
      mockFirecrawlService.scrapeLaptopProduct.mockRejectedValue(new Error('Firecrawl failed'))
      mockPuppeteerService.scrapeLaptopProduct.mockResolvedValue(mockLaptopData)

      const target: LaptopScrapingTarget = {
        url: 'https://example.com/laptop',
        siteType: 'generic',
      }

      const strategy: ScrapingStrategy = {
        primary: 'firecrawl',
        fallback: 'puppeteer',
        retryWithFallback: true,
      }

      const result = await orchestrator.scrapeLaptopWithFallback(target, strategy)

      expect(result).toEqual(mockLaptopData)
      expect(mockFirecrawlService.scrapeLaptopProduct).toHaveBeenCalledWith(target)
      expect(mockPuppeteerService.scrapeLaptopProduct).toHaveBeenCalledWith(target)
    })

    it('should use Puppeteer as primary strategy', async () => {
      mockPuppeteerService.scrapeLaptopProduct.mockResolvedValue(mockLaptopData)

      const target: LaptopScrapingTarget = {
        url: 'https://example.com/laptop',
        siteType: 'generic',
      }

      const strategy: ScrapingStrategy = {
        primary: 'puppeteer',
        fallback: 'firecrawl',
        retryWithFallback: true,
      }

      const result = await orchestrator.scrapeLaptopWithFallback(target, strategy)

      expect(result).toEqual(mockLaptopData)
      expect(mockPuppeteerService.scrapeLaptopProduct).toHaveBeenCalledWith(target)
      expect(mockFirecrawlService.scrapeLaptopProduct).not.toHaveBeenCalled()
    })

    it('should throw error when both strategies fail', async () => {
      mockFirecrawlService.scrapeLaptopProduct.mockRejectedValue(new Error('Firecrawl failed'))
      mockPuppeteerService.scrapeLaptopProduct.mockRejectedValue(new Error('Puppeteer failed'))

      const target: LaptopScrapingTarget = {
        url: 'https://example.com/laptop',
        siteType: 'generic',
      }

      const strategy: ScrapingStrategy = {
        primary: 'firecrawl',
        fallback: 'puppeteer',
        retryWithFallback: true,
      }

      await expect(orchestrator.scrapeLaptopWithFallback(target, strategy)).rejects.toThrow('Puppeteer failed')
    })

    it('should not use fallback when retryWithFallback is false', async () => {
      mockFirecrawlService.scrapeLaptopProduct.mockRejectedValue(new Error('Firecrawl failed'))

      const target: LaptopScrapingTarget = {
        url: 'https://example.com/laptop',
        siteType: 'generic',
      }

      const strategy: ScrapingStrategy = {
        primary: 'firecrawl',
        fallback: 'puppeteer',
        retryWithFallback: false,
      }

      await expect(orchestrator.scrapeLaptopWithFallback(target, strategy)).rejects.toThrow('Firecrawl failed')
      expect(mockPuppeteerService.scrapeLaptopProduct).not.toHaveBeenCalled()
    })
  })

  describe('Job management', () => {
    it('should start a scraping job and return job ID', async () => {
      const targets: LaptopScrapingTarget[] = [
        { url: 'https://example.com/laptop1', siteType: 'generic' },
        { url: 'https://example.com/laptop2', siteType: 'generic' },
      ]

      const jobId = await orchestrator.startLaptopScrapingJob(targets)

      expect(jobId).toBeDefined()
      expect(jobId).toMatch(/^scraping_\d+_[a-z0-9]+$/)

      const jobStatus = orchestrator.getJobStatus(jobId)
      expect(jobStatus).toBeDefined()
      expect(jobStatus?.status).toBe('running')
      expect(jobStatus?.progress.total).toBe(2)
    })

    it('should get job status', async () => {
      const targets: LaptopScrapingTarget[] = [
        { url: 'https://example.com/laptop1', siteType: 'generic' },
      ]

      const jobId = await orchestrator.startLaptopScrapingJob(targets)
      const jobStatus = orchestrator.getJobStatus(jobId)

      expect(jobStatus).toBeDefined()
      expect(jobStatus?.jobId).toBe(jobId)
      expect(jobStatus?.status).toBe('running')
      expect(jobStatus?.progress.total).toBe(1)
      expect(jobStatus?.progress.completed).toBe(0)
      expect(jobStatus?.progress.failed).toBe(0)
      expect(jobStatus?.results.successful).toEqual([])
      expect(jobStatus?.results.failed).toEqual([])
    })

    it('should cancel a running job', async () => {
      const targets: LaptopScrapingTarget[] = [
        { url: 'https://example.com/laptop1', siteType: 'generic' },
      ]

      const jobId = await orchestrator.startLaptopScrapingJob(targets)
      const cancelled = await orchestrator.cancelJob(jobId)

      expect(cancelled).toBe(true)

      const jobStatus = orchestrator.getJobStatus(jobId)
      expect(jobStatus?.status).toBe('cancelled')
    })

    it('should return false when cancelling non-existent job', async () => {
      const cancelled = await orchestrator.cancelJob('non-existent-job')
      expect(cancelled).toBe(false)
    })

    it('should return null for non-existent job status', () => {
      const jobStatus = orchestrator.getJobStatus('non-existent-job')
      expect(jobStatus).toBeNull()
    })
  })

  describe('URL discovery', () => {
    it('should discover laptop URLs from multiple sites', async () => {
      // Mock different responses for each site
      mockFirecrawlService.crawlForLaptops
        .mockResolvedValueOnce(['https://example.com/laptop1', 'https://example.com/laptop2'])
        .mockResolvedValueOnce(['https://bestbuy.com/laptop3'])

      const sites = [
        {
          baseUrl: 'https://example.com',
          siteType: 'generic' as const,
          maxPages: 10,
        },
        {
          baseUrl: 'https://bestbuy.com',
          siteType: 'bestbuy' as const,
          maxPages: 5,
        },
      ]

      const discoveredTargets = await orchestrator.discoverLaptopUrls(sites)

      expect(discoveredTargets).toHaveLength(3)
      expect(discoveredTargets[0].url).toBe('https://example.com/laptop1')
      expect(discoveredTargets[0].siteType).toBe('generic')
      expect(discoveredTargets[2].url).toBe('https://bestbuy.com/laptop3')
      expect(discoveredTargets[2].siteType).toBe('bestbuy')
    })

    it('should handle discovery failures gracefully', async () => {
      mockFirecrawlService.crawlForLaptops.mockRejectedValue(new Error('Crawling failed'))

      const sites = [
        {
          baseUrl: 'https://example.com',
          siteType: 'generic' as const,
        },
      ]

      const discoveredTargets = await orchestrator.discoverLaptopUrls(sites)

      expect(discoveredTargets).toHaveLength(0)
    })
  })

  describe('Multi-source search', () => {
    it('should search laptops across multiple sources', async () => {
      const mockResults: LaptopData[] = [
        {
          title: 'Gaming Laptop 1',
          url: 'https://example.com/laptop1',
          source: 'example.com',
          scrapedAt: new Date(),
        },
        {
          title: 'Gaming Laptop 2',
          url: 'https://amazon.com/laptop2',
          source: 'amazon.com',
          scrapedAt: new Date(),
        },
      ]

      mockFirecrawlService.searchLaptops
        .mockResolvedValueOnce([mockResults[0]])
        .mockResolvedValueOnce([mockResults[1]])

      const results = await orchestrator.searchLaptopsAcrossSources('gaming laptop', {
        maxResultsPerSource: 5,
        sources: ['web', 'amazon'],
      })

      expect(results).toHaveLength(2)
      expect(results[0].title).toBe('Gaming Laptop 1')
      expect(results[1].title).toBe('Gaming Laptop 2')
      expect(mockFirecrawlService.searchLaptops).toHaveBeenCalledTimes(2)
    })

    it('should handle search failures gracefully', async () => {
      mockFirecrawlService.searchLaptops.mockRejectedValue(new Error('Search failed'))

      const results = await orchestrator.searchLaptopsAcrossSources('gaming laptop', {
        sources: ['web'],
      })

      expect(results).toHaveLength(0)
    })
  })

  describe('Cleanup', () => {
    it('should clean up old completed jobs', async () => {
      const targets: LaptopScrapingTarget[] = [
        { url: 'https://example.com/laptop1', siteType: 'generic' },
      ]

      const jobId = await orchestrator.startLaptopScrapingJob(targets)

      // Cancel the job to make it completed
      await orchestrator.cancelJob(jobId)

      // Wait a bit for the job to be properly cancelled/failed
      await new Promise(resolve => setTimeout(resolve, 100))

      // Verify job exists and is in a completed state (cancelled or failed)
      const jobStatus = orchestrator.getJobStatus(jobId)
      expect(jobStatus).toBeDefined()
      expect(['cancelled', 'failed', 'completed']).toContain(jobStatus?.status)

      // Clean up with 0 max age (should remove all completed jobs)
      orchestrator.cleanupCompletedJobs(0)

      // Job should be removed
      expect(orchestrator.getJobStatus(jobId)).toBeNull()
    })

    it('should not clean up running jobs', async () => {
      const targets: LaptopScrapingTarget[] = [
        { url: 'https://example.com/laptop1', siteType: 'generic' },
      ]

      const jobId = await orchestrator.startLaptopScrapingJob(targets)

      // Clean up with 0 max age
      orchestrator.cleanupCompletedJobs(0)

      // Running job should still exist
      expect(orchestrator.getJobStatus(jobId)).toBeDefined()
      expect(orchestrator.getJobStatus(jobId)?.status).toBe('running')
    })
  })
})
