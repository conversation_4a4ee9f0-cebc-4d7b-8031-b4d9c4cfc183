// Puppeteer service for web scraping (fallback when Firecrawl fails)

import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON>, PuppeteerLaunchOptions } from 'puppeteer'
import { BaseScraper, type ScrapingTarget, type ScrapedData, type ScrapingConfig } from './base-scraper'
import type { LaptopScrapingTarget, LaptopData } from './firecrawl.service'

export interface PuppeteerConfig extends ScrapingConfig {
  headless: boolean
  viewport: { width: number; height: number }
  launchOptions: PuppeteerLaunchOptions
  blockResources: string[]
  enableJavaScript: boolean
}

export class PuppeteerService extends BaseScraper {
  private browser: Browser | null = null
  private puppeteerConfig: PuppeteerConfig

  constructor(config: Partial<PuppeteerConfig> = {}) {
    super(config)
    
    this.puppeteerConfig = {
      ...this.config,
      headless: config.headless ?? true,
      viewport: config.viewport || { width: 1920, height: 1080 },
      launchOptions: {
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu',
        ],
        ...config.launchOptions,
      },
      blockResources: config.blockResources || ['image', 'stylesheet', 'font', 'media'],
      enableJavaScript: config.enableJavaScript ?? true,
    }
  }

  /**
   * Initialize browser instance
   */
  async initialize(): Promise<void> {
    if (this.browser) return

    try {
      this.browser = await puppeteer.launch({
        headless: this.puppeteerConfig.headless,
        ...this.puppeteerConfig.launchOptions,
      })
      
      await this.logActivity('info', 'Puppeteer browser initialized')
    } catch (error) {
      await this.logActivity('error', `Failed to initialize browser: ${error}`)
      throw error
    }
  }

  /**
   * Close browser instance
   */
  async close(): Promise<void> {
    if (this.browser) {
      await this.browser.close()
      this.browser = null
      await this.logActivity('info', 'Puppeteer browser closed')
    }
  }

  /**
   * Scrape a single URL using Puppeteer
   */
  async scrape(target: ScrapingTarget): Promise<ScrapedData> {
    if (!this.validateUrl(target.url)) {
      throw new Error(`Invalid URL: ${target.url}`)
    }

    await this.initialize()
    
    if (!this.browser) {
      throw new Error('Browser not initialized')
    }

    const page = await this.browser.newPage()

    try {
      await this.logActivity('info', `Starting Puppeteer scrape`, target.url)

      // Configure page
      await this.configurePage(page)

      // Navigate to URL
      await page.goto(target.url, {
        waitUntil: 'networkidle2',
        timeout: this.config.timeout,
      })

      // Wait for specific selector if provided
      if (target.selector) {
        await page.waitForSelector(target.selector, { timeout: 10000 })
      }

      // Wait for additional time if specified
      if (target.waitFor) {
        await page.waitForTimeout(target.waitFor)
      }

      // Execute actions if provided
      if (target.actions) {
        await this.executeActions(page, target.actions)
      }

      // Extract content
      const content = await this.extractContent(page, target.selector)
      const title = await page.title()
      const metadata = this.extractMetadata(content, target.url)

      // Add page-specific metadata
      const url = page.url()
      const viewport = page.viewport()
      
      Object.assign(metadata, {
        puppeteer: {
          finalUrl: url,
          title,
          viewport,
          userAgent: await page.evaluate(() => navigator.userAgent),
        },
      })

      await this.logActivity('info', `Successfully scraped ${content.length} characters`, target.url)

      return {
        url: target.url,
        title,
        content: this.cleanText(content),
        metadata,
        timestamp: new Date(),
        success: true,
      }
    } catch (error) {
      await this.logActivity('error', `Puppeteer scraping failed: ${error}`, target.url)
      throw error
    } finally {
      await page.close()
    }
  }

  /**
   * Scrape laptop product pages with structured data extraction
   */
  async scrapeLaptopProducts(targets: LaptopScrapingTarget[]): Promise<LaptopData[]> {
    await this.initialize()
    const results: LaptopData[] = []

    for (const target of targets) {
      try {
        const laptopData = await this.scrapeLaptopProduct(target)
        results.push(laptopData)

        // Rate limiting
        if (this.config.rateLimit > 0) {
          await new Promise(resolve => setTimeout(resolve, this.config.rateLimit))
        }
      } catch (error) {
        console.error(`Failed to scrape laptop from ${target.url}:`, error)
        await this.logActivity('error', `Failed to scrape laptop: ${error}`, target.url)
      }
    }

    return results
  }

  /**
   * Scrape a single laptop product page
   */
  async scrapeLaptopProduct(target: LaptopScrapingTarget): Promise<LaptopData> {
    if (!this.browser) {
      throw new Error('Browser not initialized')
    }

    const page = await this.browser.newPage()

    try {
      await this.configurePage(page)
      await page.goto(target.url, { waitUntil: 'networkidle2', timeout: this.config.timeout })

      // Wait for product content to load
      await page.waitForTimeout(3000)

      const laptopData = await this.extractLaptopDataFromPage(page, target)
      
      return laptopData
    } catch (error) {
      await this.logActivity('error', `Failed to scrape laptop product: ${error}`, target.url)
      throw error
    } finally {
      await page.close()
    }
  }

  /**
   * Take screenshot of a page
   */
  async takeScreenshot(
    url: string,
    options: {
      fullPage?: boolean
      selector?: string
      path?: string
    } = {}
  ): Promise<Buffer> {
    await this.initialize()
    
    if (!this.browser) {
      throw new Error('Browser not initialized')
    }

    const page = await this.browser.newPage()

    try {
      await this.configurePage(page)
      await page.goto(url, { waitUntil: 'networkidle2' })

      let screenshotOptions: any = {
        type: 'png',
        fullPage: options.fullPage ?? false,
      }

      if (options.path) {
        screenshotOptions.path = options.path
      }

      if (options.selector) {
        const element = await page.$(options.selector)
        if (element) {
          return await element.screenshot(screenshotOptions)
        }
      }

      return await page.screenshot(screenshotOptions)
    } finally {
      await page.close()
    }
  }

  // Private helper methods

  private async configurePage(page: Page): Promise<void> {
    // Set viewport
    await page.setViewport(this.puppeteerConfig.viewport)

    // Set user agent
    await page.setUserAgent(this.config.userAgent)

    // Set extra headers
    await page.setExtraHTTPHeaders(this.config.headers)

    // Block unnecessary resources for faster loading
    if (this.puppeteerConfig.blockResources.length > 0) {
      await page.setRequestInterception(true)
      
      page.on('request', (request) => {
        const resourceType = request.resourceType()
        if (this.puppeteerConfig.blockResources.includes(resourceType)) {
          request.abort()
        } else {
          request.continue()
        }
      })
    }

    // Disable JavaScript if configured
    if (!this.puppeteerConfig.enableJavaScript) {
      await page.setJavaScriptEnabled(false)
    }
  }

  private async executeActions(
    page: Page,
    actions: Array<{
      type: 'click' | 'scroll' | 'wait' | 'type'
      selector?: string
      value?: string | number
    }>
  ): Promise<void> {
    for (const action of actions) {
      try {
        switch (action.type) {
          case 'click':
            if (action.selector) {
              await page.click(action.selector)
            }
            break
          
          case 'scroll':
            if (action.selector) {
              await page.evaluate((selector) => {
                const element = document.querySelector(selector)
                if (element) {
                  element.scrollIntoView()
                }
              }, action.selector)
            } else {
              await page.evaluate(() => {
                window.scrollTo(0, document.body.scrollHeight)
              })
            }
            break
          
          case 'wait':
            await page.waitForTimeout(Number(action.value) || 1000)
            break
          
          case 'type':
            if (action.selector && action.value) {
              await page.type(action.selector, String(action.value))
            }
            break
        }
        
        // Small delay between actions
        await page.waitForTimeout(500)
      } catch (error) {
        console.warn(`Action ${action.type} failed:`, error)
      }
    }
  }

  private async extractContent(page: Page, selector?: string): Promise<string> {
    if (selector) {
      try {
        return await page.$eval(selector, (el) => el.textContent || '')
      } catch {
        // Fallback to full page content if selector fails
      }
    }

    // Extract main content, excluding navigation, footer, etc.
    return await page.evaluate(() => {
      const elementsToRemove = [
        'nav', 'header', 'footer', 'aside', 'script', 'style', 'noscript'
      ]
      
      elementsToRemove.forEach(tag => {
        const elements = document.querySelectorAll(tag)
        elements.forEach(el => el.remove())
      })

      return document.body.textContent || ''
    })
  }

  private async extractLaptopDataFromPage(
    page: Page,
    target: LaptopScrapingTarget
  ): Promise<LaptopData> {
    const url = page.url()
    const title = await page.title()

    // Extract structured data using site-specific selectors
    const selectors = this.getSiteSelectors(target.siteType)
    
    const laptopData: LaptopData = {
      title: title || 'Unknown Laptop',
      url,
      source: this.extractDomain(url),
      scrapedAt: new Date(),
    }

    try {
      // Extract title
      if (selectors.title) {
        const titleText = await page.$eval(selectors.title, el => el.textContent?.trim())
        if (titleText) laptopData.title = titleText
      }

      // Extract price
      if (selectors.price) {
        const priceText = await page.$eval(selectors.price, el => el.textContent?.trim()).catch(() => null)
        if (priceText) {
          const priceMatch = priceText.match(/[\d,]+\.?\d*/)
          if (priceMatch) {
            laptopData.price = {
              current: parseFloat(priceMatch[0].replace(/,/g, '')),
              currency: 'USD',
            }
          }
        }
      }

      // Extract specifications
      if (selectors.specs) {
        const specsText = await page.$eval(selectors.specs, el => el.textContent?.trim()).catch(() => null)
        if (specsText) {
          laptopData.specifications = this.parseSpecifications(specsText)
        }
      }

      // Extract images
      if (selectors.images) {
        const images = await page.$$eval(selectors.images, els => 
          els.map(el => (el as HTMLImageElement).src).filter(src => src)
        ).catch(() => [])
        if (images.length > 0) {
          laptopData.images = images
        }
      }

      // Extract availability
      if (selectors.availability) {
        const availabilityText = await page.$eval(selectors.availability, el => el.textContent?.trim()).catch(() => null)
        if (availabilityText) {
          laptopData.availability = {
            inStock: !availabilityText.toLowerCase().includes('out of stock'),
            shippingInfo: availabilityText,
          }
        }
      }

    } catch (error) {
      console.warn('Error extracting structured data:', error)
    }

    return laptopData
  }

  private getSiteSelectors(siteType: LaptopScrapingTarget['siteType']): {
    title?: string
    price?: string
    specs?: string
    images?: string
    availability?: string
  } {
    switch (siteType) {
      case 'amazon':
        return {
          title: '#productTitle',
          price: '.a-price-whole, .a-offscreen',
          specs: '#feature-bullets ul, #productDetails_techSpec_section_1',
          images: '#landingImage, .a-dynamic-image',
          availability: '#availability span',
        }
      
      case 'bestbuy':
        return {
          title: '.sku-title h1',
          price: '.sr-only:contains("current price"), .pricing-price__range',
          specs: '.product-data-value',
          images: '.primary-image, .carousel-image',
          availability: '.fulfillment-add-to-cart-button',
        }
      
      case 'newegg':
        return {
          title: '.product-title',
          price: '.price-current',
          specs: '.table-horizontal',
          images: '.product-view-img-original',
          availability: '.product-inventory',
        }
      
      default:
        return {
          title: 'h1, .product-title, .title',
          price: '.price, .cost, .amount',
          specs: '.specs, .specifications, .features',
          images: '.product-image img, .gallery img',
          availability: '.availability, .stock, .inventory',
        }
    }
  }

  private parseSpecifications(specsText: string): Record<string, any> {
    const specs: Record<string, any> = {}
    
    // Extract RAM
    const ramMatch = specsText.match(/(\d+)\s*GB\s*(RAM|Memory)/i)
    if (ramMatch) {
      specs.ram = `${ramMatch[1]}GB`
    }

    // Extract Storage
    const storageMatch = specsText.match(/(\d+)\s*(GB|TB)\s*(SSD|HDD|Storage)/i)
    if (storageMatch) {
      specs.storage = `${storageMatch[1]}${storageMatch[2]} ${storageMatch[3]}`
    }

    // Extract CPU
    const cpuMatch = specsText.match(/(Intel|AMD)\s+([^,\n]+)/i)
    if (cpuMatch) {
      specs.cpu = this.cleanText(cpuMatch[0])
    }

    // Extract GPU
    const gpuMatch = specsText.match(/(NVIDIA|AMD|Intel)\s+(GeForce|Radeon|Iris|UHD)\s+([^,\n]+)/i)
    if (gpuMatch) {
      specs.gpu = this.cleanText(gpuMatch[0])
    }

    // Extract Display
    const displayMatch = specsText.match(/(\d+\.?\d*)\s*["']\s*(.*?)(display|screen|monitor)/i)
    if (displayMatch) {
      specs.display = `${displayMatch[1]}" ${displayMatch[2]}`
    }

    return specs
  }
}
