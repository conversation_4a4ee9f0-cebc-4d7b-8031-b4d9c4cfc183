import type { Metada<PERSON> } from "next";
import { Inter } from 'next/font/google'
import "./globals.css";
import { QueryProvider } from '@/providers/query-provider'
import { AppProvider } from '@/contexts/app-context'
import { cn } from '@/lib/utils'

const inter = Inter({ subsets: ['latin'], variable: '--font-inter' })

export const metadata: Metadata = {
  title: "LaptopLLM Finder - Find the Perfect Laptop for Running LLMs",
  description: "Discover and compare laptops optimized for running Large Language Models locally. Find the best hardware for your AI projects with our comprehensive compatibility analysis.",
  keywords: ["laptop", "LLM", "AI", "machine learning", "hardware", "compatibility", "local AI"],
  authors: [{ name: "LaptopLLM Team" }],
  openGraph: {
    title: "LaptopLLM Finder",
    description: "Find the perfect laptop for running Large Language Models locally",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={cn(
        "min-h-screen bg-background font-sans antialiased",
        inter.variable
      )}>
        <QueryProvider>
          <AppProvider>
            {children}
          </AppProvider>
        </QueryProvider>
      </body>
    </html>
  )
}