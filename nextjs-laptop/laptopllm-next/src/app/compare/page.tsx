// Compare Page - Side-by-side laptop comparison

'use client'

import React, { useState } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Separator } from '@/components/ui/separator'
import { Progress } from '@/components/ui/progress'
import {
  Search,
  Plus,
  X,
  TrendingUp,
  TrendingDown,
  Minus,
  CheckCircle,
  AlertTriangle,
  Info
} from 'lucide-react'
import type { LaptopData } from '@/lib/scraping/firecrawl.service'
import type { CompatibilityScore } from '@/lib/llm/compatibility.service'
import { useComparison } from '@/contexts/app-context'
import { useLaptops, useSearchLaptops, useCompatibilityScore } from '@/hooks/use-laptops'



const mockCompatibilityScores: CompatibilityScore[] = [
  {
    overall: 78,
    breakdown: {
      memory: 75,
      processing: 85,
      graphics: 80,
      storage: 85,
      thermals: 70,
    },
    estimatedPerformance: {
      tokensPerSecond: 42,
      memoryUsage: 14,
      powerConsumption: 165,
    },
    warnings: ['High power consumption during intensive tasks'],
    recommendations: ['Consider external cooling for extended use'],
  },
  {
    overall: 85,
    breakdown: {
      memory: 90,
      processing: 88,
      graphics: 75,
      storage: 90,
      thermals: 95,
    },
    estimatedPerformance: {
      tokensPerSecond: 38,
      memoryUsage: 12,
      powerConsumption: 85,
    },
    warnings: [],
    recommendations: ['Excellent for most LLM applications'],
  },
]

export default function ComparePage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [showSearch, setShowSearch] = useState(false)

  // Use context and hooks
  const comparison = useComparison()
  const { data: laptops = [], isLoading } = useLaptops()
  const { data: searchResults = [] } = useSearchLaptops(searchQuery)

  // Get laptops from comparison context
  const selectedLaptops = comparison.laptops

  const addLaptop = (laptop: LaptopData) => {
    if (!comparison.isFull && !comparison.isInComparison(laptop.url)) {
      comparison.addLaptop(laptop)
    }
    setShowSearch(false)
    setSearchQuery('')
  }

  const removeLaptop = (index: number) => {
    const laptop = selectedLaptops[index]
    if (laptop) {
      comparison.removeLaptop(laptop.url)
    }
  }

  const getCompatibilityLabel = (score: number) => {
    if (score >= 90) return { label: 'Excellent', color: 'text-green-600' }
    if (score >= 75) return { label: 'Good', color: 'text-blue-600' }
    if (score >= 60) return { label: 'Fair', color: 'text-yellow-600' }
    return { label: 'Limited', color: 'text-red-600' }
  }

  // Filter available laptops (exclude already selected ones)
  const availableLaptops = searchQuery.length > 2 ? searchResults : laptops
  const filteredLaptops = availableLaptops.filter(laptop =>
    !comparison.isInComparison(laptop.url) &&
    (laptop.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
     laptop.brand.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  const formatPrice = (price: { current: number; original?: number; currency: string }) => {
    const formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: price.currency,
    })
    return formatter.format(price.current)
  }

  const ComparisonIcon = ({ value1, value2 }: { value1: number; value2: number }) => {
    if (value1 > value2) return <TrendingUp className="h-4 w-4 text-green-600" />
    if (value1 < value2) return <TrendingDown className="h-4 w-4 text-red-600" />
    return <Minus className="h-4 w-4 text-muted-foreground" />
  }

  return (
    <MainLayout>
      <div className="min-h-screen bg-background">
        {/* Header */}
        <section className="border-b bg-muted/30">
          <div className="container mx-auto px-4 py-8">
            <div className="space-y-4">
              <div>
                <h1 className="text-3xl font-bold mb-2">Compare Laptops</h1>
                <p className="text-muted-foreground">
                  Compare specifications, compatibility scores, and performance side by side
                </p>
              </div>

              {/* Add Laptop Button */}
              {selectedLaptops.length < 4 && (
                <Button onClick={() => setShowSearch(!showSearch)} variant="outline">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Laptop to Compare
                </Button>
              )}

              {/* Search Interface */}
              {showSearch && (
                <Card className="p-4">
                  <div className="flex gap-2">
                    <div className="relative flex-1">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search for laptops to add..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                    <Button variant="ghost" onClick={() => setShowSearch(false)}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  {/* Search results */}
                  <div className="mt-4 space-y-2">
                    {isLoading ? (
                      <div className="text-center py-4 text-muted-foreground">Loading laptops...</div>
                    ) : filteredLaptops.length === 0 ? (
                      <div className="text-center py-4 text-muted-foreground">
                        {searchQuery ? 'No laptops found matching your search' : 'No laptops available'}
                      </div>
                    ) : (
                      filteredLaptops.map((laptop, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-2 hover:bg-muted rounded cursor-pointer"
                          onClick={() => addLaptop(laptop)}
                        >
                          <div>
                            <div className="font-medium">{laptop.title}</div>
                            <div className="text-sm text-muted-foreground">{laptop.brand}</div>
                          </div>
                          <Button size="sm" variant="ghost">
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                      ))
                    )}
                  </div>
                </Card>
              )}
            </div>
          </div>
        </section>

        {/* Comparison Table */}
        <div className="container mx-auto px-4 py-8">
          {selectedLaptops.length === 0 ? (
            <Card className="text-center py-12">
              <CardContent>
                <Info className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No laptops selected</h3>
                <p className="text-muted-foreground mb-4">
                  Add laptops to start comparing their specifications and compatibility scores.
                </p>
                <Button onClick={() => setShowSearch(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Your First Laptop
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="overflow-x-auto">
              <div className="min-w-full">
                <div className="grid gap-6" style={{ gridTemplateColumns: `200px repeat(${selectedLaptops.length}, 1fr)` }}>
                  {/* Header Row */}
                  <div className="font-semibold text-muted-foreground">Laptop</div>
                  {selectedLaptops.map((laptop, index) => (
                    <Card key={index} className="relative">
                      <CardHeader className="pb-4">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="absolute top-2 right-2 h-6 w-6 p-0"
                          onClick={() => removeLaptop(index)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                        <CardTitle className="text-lg pr-8">{laptop.title}</CardTitle>
                        <CardDescription>{laptop.brand}</CardDescription>
                        <div className="text-lg font-bold text-primary">
                          {formatPrice(laptop.price!)}
                        </div>
                      </CardHeader>
                    </Card>
                  ))}

                  {/* LLM Compatibility */}
                  <div className="font-semibold">LLM Compatibility</div>
                  {selectedLaptops.map((_, index) => (
                    <Card key={index}>
                      <CardContent className="pt-6">
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <span className="text-sm">Overall Score</span>
                            <div className="flex items-center gap-2">
                              <span className={`font-semibold ${getCompatibilityLabel(mockCompatibilityScores[index]?.overall || 0).color}`}>
                                {mockCompatibilityScores[index]?.overall || 0}%
                              </span>
                              <Badge variant="secondary">
                                {getCompatibilityLabel(mockCompatibilityScores[index]?.overall || 0).label}
                              </Badge>
                            </div>
                          </div>
                          <Progress value={mockCompatibilityScores[index]?.overall || 0} className="h-2" />
                          
                          {/* Performance Estimate */}
                          <div className="text-sm space-y-1">
                            <div className="flex justify-between">
                              <span>Est. Tokens/sec:</span>
                              <span className="font-medium">
                                {mockCompatibilityScores[index]?.estimatedPerformance?.tokensPerSecond || 0}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span>Memory Usage:</span>
                              <span className="font-medium">
                                {mockCompatibilityScores[index]?.estimatedPerformance?.memoryUsage || 0}GB
                              </span>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}

                  {/* Specifications */}
                  <Separator className="col-span-full my-4" />
                  
                  <div className="font-semibold">Specifications</div>
                  <div className="col-span-full" />

                  {/* CPU */}
                  <div className="text-sm font-medium">CPU</div>
                  {selectedLaptops.map((laptop, index) => (
                    <Card key={index}>
                      <CardContent className="pt-6">
                        <div className="text-sm">{laptop.specifications?.cpu || 'N/A'}</div>
                      </CardContent>
                    </Card>
                  ))}

                  {/* RAM */}
                  <div className="text-sm font-medium">RAM</div>
                  {selectedLaptops.map((laptop, index) => (
                    <Card key={index}>
                      <CardContent className="pt-6">
                        <div className="text-sm">{laptop.specifications?.ram || 'N/A'}</div>
                      </CardContent>
                    </Card>
                  ))}

                  {/* GPU */}
                  <div className="text-sm font-medium">GPU</div>
                  {selectedLaptops.map((laptop, index) => (
                    <Card key={index}>
                      <CardContent className="pt-6">
                        <div className="text-sm">{laptop.specifications?.gpu || 'N/A'}</div>
                      </CardContent>
                    </Card>
                  ))}

                  {/* Storage */}
                  <div className="text-sm font-medium">Storage</div>
                  {selectedLaptops.map((laptop, index) => (
                    <Card key={index}>
                      <CardContent className="pt-6">
                        <div className="text-sm">{laptop.specifications?.storage || 'N/A'}</div>
                      </CardContent>
                    </Card>
                  ))}

                  {/* Display */}
                  <div className="text-sm font-medium">Display</div>
                  {selectedLaptops.map((laptop, index) => (
                    <Card key={index}>
                      <CardContent className="pt-6">
                        <div className="text-sm">{laptop.specifications?.display || 'N/A'}</div>
                      </CardContent>
                    </Card>
                  ))}

                  {/* Weight */}
                  <div className="text-sm font-medium">Weight</div>
                  {selectedLaptops.map((laptop, index) => (
                    <Card key={index}>
                      <CardContent className="pt-6">
                        <div className="text-sm">{laptop.specifications?.weight || 'N/A'}</div>
                      </CardContent>
                    </Card>
                  ))}

                  {/* Screen Size */}
                  <div className="text-sm font-medium">Screen Size</div>
                  {selectedLaptops.map((laptop, index) => (
                    <Card key={index}>
                      <CardContent className="pt-6">
                        <div className="text-sm">{laptop.screenSize}" {laptop.screenResolution}</div>
                      </CardContent>
                    </Card>
                  ))}

                  {/* Weight */}
                  <div className="text-sm font-medium">Weight</div>
                  {selectedLaptops.map((laptop, index) => (
                    <Card key={index}>
                      <CardContent className="pt-6">
                        <div className="text-sm">{laptop.weightKg}kg</div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  )
}
