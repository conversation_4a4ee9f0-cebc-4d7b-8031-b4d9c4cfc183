// React Query hooks for laptop data management

'use client'

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import type { LaptopData } from '@/lib/scraping/firecrawl.service'
import type { RecommendationCriteria, LaptopRecommendation } from '@/lib/llm/recommendation.service'
import type { CompatibilityScore } from '@/lib/llm/compatibility.service'

// Query keys for React Query
export const laptopKeys = {
  all: ['laptops'] as const,
  lists: () => [...laptopKeys.all, 'list'] as const,
  list: (filters: string) => [...laptopKeys.lists(), { filters }] as const,
  details: () => [...laptopKeys.all, 'detail'] as const,
  detail: (id: string) => [...laptopKeys.details(), id] as const,
  search: (query: string) => [...laptopKeys.all, 'search', query] as const,
  recommendations: () => [...laptopKeys.all, 'recommendations'] as const,
  recommendation: (criteria: RecommendationCriteria) => [...laptopKeys.recommendations(), criteria] as const,
  compatibility: () => [...laptopKeys.all, 'compatibility'] as const,
  compatibilityScore: (laptopId: string, modelName: string) => [...laptopKeys.compatibility(), laptopId, modelName] as const,
}

// Mock API functions (to be replaced with actual API calls)
const mockApi = {
  async getLaptops(filters?: Record<string, any>): Promise<LaptopData[]> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Mock data
    return [
      {
        title: 'ASUS ROG Strix G15',
        brand: 'ASUS',
        url: 'https://example.com/asus-rog-strix-g15',
        source: 'example.com',
        scrapedAt: new Date(),
        price: { current: 1299, currency: 'USD' },
        specifications: {
          ram: '16GB DDR4',
          cpu: 'AMD Ryzen 7 6800H 8-core 3.2GHz',
          gpu: 'NVIDIA GeForce RTX 3060 6GB',
          storage: '512GB NVMe SSD',
          display: '15.6 inch FHD 144Hz',
          battery: '90Wh',
          weight: '2.3kg',
        },
        image: 'https://example.com/asus-rog.jpg',
      },
      {
        title: 'MacBook Pro 16-inch M2 Pro',
        brand: 'Apple',
        url: 'https://example.com/macbook-pro-16',
        source: 'apple.com',
        scrapedAt: new Date(),
        price: { current: 2499, original: 2699, currency: 'USD' },
        specifications: {
          ram: '16GB Unified Memory',
          cpu: 'Apple M2 Pro 12-core',
          gpu: 'Apple M2 Pro 19-core GPU',
          storage: '512GB SSD',
          display: '16.2 inch Liquid Retina XDR',
          battery: '100Wh',
          weight: '2.15kg',
        },
        image: 'https://example.com/macbook-pro.jpg',
      },
    ]
  },

  async getLaptopById(id: string): Promise<LaptopData | null> {
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const laptops = await this.getLaptops()
    return laptops.find(laptop => laptop.url.includes(id)) || null
  },

  async searchLaptops(query: string): Promise<LaptopData[]> {
    await new Promise(resolve => setTimeout(resolve, 800))
    
    const laptops = await this.getLaptops()
    return laptops.filter(laptop => 
      laptop.title.toLowerCase().includes(query.toLowerCase()) ||
      laptop.brand.toLowerCase().includes(query.toLowerCase())
    )
  },

  async getRecommendations(criteria: RecommendationCriteria): Promise<LaptopRecommendation[]> {
    await new Promise(resolve => setTimeout(resolve, 1200))
    
    const laptops = await this.getLaptops()
    return laptops.map((laptop, index) => ({
      laptop,
      compatibilityScore: {
        overall: 85 - index * 10,
        breakdown: {
          memory: 80,
          processing: 90,
          graphics: 85,
          storage: 90,
          thermals: 75,
        },
        estimatedPerformance: {
          tokensPerSecond: 45 - index * 5,
          memoryUsage: 12 + index * 2,
          powerConsumption: 120 + index * 20,
        },
        warnings: index > 0 ? ['May require external cooling'] : [],
        recommendations: ['Excellent for most LLM applications'],
      },
      overallScore: 85 - index * 10,
      priceScore: 80,
      criteriaScore: 90,
      pros: ['High performance', 'Good value'],
      cons: ['High power consumption'],
      bestUseCase: 'Development and experimentation',
      insights: {
        marketPosition: 'Mid-range gaming laptop',
        valueProposition: 'Good balance of performance and price',
        competitiveAdvantages: ['Strong CPU performance', 'Adequate cooling'],
      },
    }))
  },

  async getCompatibilityScore(laptopId: string, modelName: string): Promise<CompatibilityScore> {
    await new Promise(resolve => setTimeout(resolve, 600))
    
    return {
      overall: 78,
      breakdown: {
        memory: 75,
        processing: 85,
        graphics: 80,
        storage: 85,
        thermals: 70,
      },
      estimatedPerformance: {
        tokensPerSecond: 42,
        memoryUsage: 14,
        powerConsumption: 165,
      },
      warnings: ['High power consumption during intensive tasks'],
      recommendations: ['Consider external cooling for extended use'],
    }
  },
}

// Custom hooks
export function useLaptops(filters?: Record<string, any>) {
  return useQuery({
    queryKey: laptopKeys.list(JSON.stringify(filters || {})),
    queryFn: () => mockApi.getLaptops(filters),
    staleTime: 10 * 60 * 1000, // 10 minutes - laptop lists are relatively stable
    gcTime: 60 * 60 * 1000, // 1 hour - keep in cache longer
    refetchOnMount: false, // Don't refetch on every mount
    refetchOnWindowFocus: false,
  })
}

export function useLaptop(id: string) {
  return useQuery({
    queryKey: laptopKeys.detail(id),
    queryFn: () => mockApi.getLaptopById(id),
    enabled: !!id,
    staleTime: 15 * 60 * 1000, // 15 minutes - individual laptop details change less frequently
    gcTime: 2 * 60 * 60 * 1000, // 2 hours - keep individual laptops in cache longer
    refetchOnMount: false,
    refetchOnWindowFocus: false,
  })
}

export function useSearchLaptops(query: string) {
  return useQuery({
    queryKey: laptopKeys.search(query),
    queryFn: () => mockApi.searchLaptops(query),
    enabled: query.length > 2, // Only search if query is longer than 2 characters
    staleTime: 3 * 60 * 1000, // 3 minutes - search results can be cached a bit longer
    gcTime: 15 * 60 * 1000, // 15 minutes - keep search results in cache
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    // Keep previous data while fetching new results for better UX
    placeholderData: (previousData) => previousData,
  })
}

export function useRecommendations(criteria: RecommendationCriteria) {
  return useQuery({
    queryKey: laptopKeys.recommendation(criteria),
    queryFn: () => mockApi.getRecommendations(criteria),
    staleTime: 15 * 60 * 1000, // 15 minutes - recommendations are relatively stable
    gcTime: 60 * 60 * 1000, // 1 hour - keep recommendations in cache longer
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    // Keep previous data while fetching new recommendations
    placeholderData: (previousData) => previousData,
  })
}

export function useCompatibilityScore(laptopId: string, modelName: string) {
  return useQuery({
    queryKey: laptopKeys.compatibilityScore(laptopId, modelName),
    queryFn: () => mockApi.getCompatibilityScore(laptopId, modelName),
    enabled: !!laptopId && !!modelName,
    staleTime: 30 * 60 * 1000, // 30 minutes - compatibility scores are very stable
    gcTime: 2 * 60 * 60 * 1000, // 2 hours - keep compatibility scores in cache longer
    refetchOnMount: false,
    refetchOnWindowFocus: false,
  })
}

// Mutation hooks for data modification
export function useRefreshLaptops() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async () => {
      // Simulate refresh operation
      await new Promise(resolve => setTimeout(resolve, 2000))
      return true
    },
    onSuccess: () => {
      // Invalidate all laptop queries to trigger refetch
      queryClient.invalidateQueries({ queryKey: laptopKeys.all })
    },
  })
}

export function useFavoriteLaptop() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async ({ laptopId, isFavorite }: { laptopId: string; isFavorite: boolean }) => {
      // Simulate API call to update favorite status
      await new Promise(resolve => setTimeout(resolve, 500))
      return { laptopId, isFavorite }
    },
    onSuccess: (data) => {
      // Update the laptop detail cache
      queryClient.setQueryData(
        laptopKeys.detail(data.laptopId),
        (oldData: LaptopData | undefined) => {
          if (oldData) {
            return {
              ...oldData,
              isFavorite: data.isFavorite,
            }
          }
          return oldData
        }
      )
    },
  })
}

// Utility hooks
export function usePrefetchLaptop(id: string) {
  const queryClient = useQueryClient()
  
  return () => {
    queryClient.prefetchQuery({
      queryKey: laptopKeys.detail(id),
      queryFn: () => mockApi.getLaptopById(id),
      staleTime: 5 * 60 * 1000,
    })
  }
}

export function useInvalidateLaptops() {
  const queryClient = useQueryClient()
  
  return () => {
    queryClient.invalidateQueries({ queryKey: laptopKeys.all })
  }
}

// Optimistic updates helper
export function useOptimisticLaptopUpdate() {
  const queryClient = useQueryClient()

  return (laptopId: string, updates: Partial<LaptopData>) => {
    queryClient.setQueryData(
      laptopKeys.detail(laptopId),
      (oldData: LaptopData | undefined) => {
        if (oldData) {
          return { ...oldData, ...updates }
        }
        return oldData
      }
    )
  }
}

// Prefetching utilities for performance optimization
export function usePrefetchLaptops() {
  const queryClient = useQueryClient()

  return (laptopId: string) => {
    queryClient.prefetchQuery({
      queryKey: laptopKeys.detail(laptopId),
      queryFn: () => mockApi.getLaptopById(laptopId),
      staleTime: 15 * 60 * 1000, // 15 minutes
    })
  }
}

export function usePrefetchCompatibilityScore() {
  const queryClient = useQueryClient()

  return (laptopId: string, modelName: string) => {
    queryClient.prefetchQuery({
      queryKey: laptopKeys.compatibilityScore(laptopId, modelName),
      queryFn: () => mockApi.getCompatibilityScore(laptopId, modelName),
      staleTime: 30 * 60 * 1000, // 30 minutes
    })
  }
}

// Background refetch utilities
export function useBackgroundRefresh() {
  const queryClient = useQueryClient()

  return {
    refreshLaptops: () => {
      queryClient.invalidateQueries({
        queryKey: laptopKeys.lists(),
        refetchType: 'active' // Only refetch active queries
      })
    },
    refreshRecommendations: () => {
      queryClient.invalidateQueries({
        queryKey: laptopKeys.recommendations(),
        refetchType: 'active'
      })
    },
    refreshAll: () => {
      queryClient.invalidateQueries({
        queryKey: laptopKeys.all,
        refetchType: 'active'
      })
    }
  }
}
