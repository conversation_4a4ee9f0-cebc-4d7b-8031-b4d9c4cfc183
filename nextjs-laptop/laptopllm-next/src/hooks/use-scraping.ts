// React Query hooks for scraping operations

'use client'

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

// Types for scraping operations
export interface ScrapingJob {
  id: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
  progress: number
  totalUrls: number
  processedUrls: number
  successfulUrls: number
  failedUrls: number
  startedAt: Date
  completedAt?: Date
  error?: string
  results?: any[]
}

export interface ScrapingJobRequest {
  urls: string[]
  options?: {
    retryWithFallback?: boolean
    maxRetries?: number
    delayBetweenRequests?: number
  }
}

export interface DiscoveredUrls {
  source: string
  urls: string[]
  discoveredAt: Date
}

// Query keys
export const scrapingKeys = {
  all: ['scraping'] as const,
  jobs: () => [...scrapingKeys.all, 'jobs'] as const,
  job: (id: string) => [...scrapingKeys.jobs(), id] as const,
  discovery: () => [...scrapingKeys.all, 'discovery'] as const,
  discoveredUrls: (source: string) => [...scrapingKeys.discovery(), source] as const,
  stats: () => [...scrapingKeys.all, 'stats'] as const,
}

// Mock API functions
const mockScrapingApi = {
  async getJobs(): Promise<ScrapingJob[]> {
    await new Promise(resolve => setTimeout(resolve, 500))
    
    return [
      {
        id: 'job_1',
        status: 'completed',
        progress: 100,
        totalUrls: 50,
        processedUrls: 50,
        successfulUrls: 45,
        failedUrls: 5,
        startedAt: new Date(Date.now() - 3600000), // 1 hour ago
        completedAt: new Date(Date.now() - 3000000), // 50 minutes ago
      },
      {
        id: 'job_2',
        status: 'running',
        progress: 65,
        totalUrls: 30,
        processedUrls: 19,
        successfulUrls: 17,
        failedUrls: 2,
        startedAt: new Date(Date.now() - 600000), // 10 minutes ago
      },
    ]
  },

  async getJob(id: string): Promise<ScrapingJob | null> {
    await new Promise(resolve => setTimeout(resolve, 300))
    
    const jobs = await this.getJobs()
    return jobs.find(job => job.id === id) || null
  },

  async startScrapingJob(request: ScrapingJobRequest): Promise<{ jobId: string }> {
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const jobId = `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    return { jobId }
  },

  async cancelJob(jobId: string): Promise<boolean> {
    await new Promise(resolve => setTimeout(resolve, 500))
    return true
  },

  async discoverUrls(sources: string[]): Promise<DiscoveredUrls[]> {
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    return sources.map(source => ({
      source,
      urls: [
        `${source}/laptop-1`,
        `${source}/laptop-2`,
        `${source}/laptop-3`,
      ],
      discoveredAt: new Date(),
    }))
  },

  async getScrapingStats(): Promise<{
    totalJobs: number
    activeJobs: number
    completedJobs: number
    failedJobs: number
    totalLaptopsScraped: number
    successRate: number
    averageJobDuration: number
  }> {
    await new Promise(resolve => setTimeout(resolve, 400))
    
    return {
      totalJobs: 25,
      activeJobs: 2,
      completedJobs: 20,
      failedJobs: 3,
      totalLaptopsScraped: 1250,
      successRate: 0.89,
      averageJobDuration: 1800, // seconds
    }
  },
}

// Custom hooks
export function useScrapingJobs() {
  return useQuery({
    queryKey: scrapingKeys.jobs(),
    queryFn: () => mockScrapingApi.getJobs(),
    refetchInterval: 5000, // Refetch every 5 seconds for real-time updates
    staleTime: 1000, // Consider data stale after 1 second
  })
}

export function useScrapingJob(jobId: string) {
  return useQuery({
    queryKey: scrapingKeys.job(jobId),
    queryFn: () => mockScrapingApi.getJob(jobId),
    enabled: !!jobId,
    refetchInterval: (data) => {
      // Only refetch if job is still running
      return data?.status === 'running' || data?.status === 'pending' ? 2000 : false
    },
    staleTime: 1000,
  })
}

export function useDiscoveredUrls(source: string) {
  return useQuery({
    queryKey: scrapingKeys.discoveredUrls(source),
    queryFn: () => mockScrapingApi.discoverUrls([source]),
    enabled: !!source,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  })
}

export function useScrapingStats() {
  return useQuery({
    queryKey: scrapingKeys.stats(),
    queryFn: () => mockScrapingApi.getScrapingStats(),
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 30000, // Refetch every 30 seconds
  })
}

// Mutation hooks
export function useStartScrapingJob() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (request: ScrapingJobRequest) => mockScrapingApi.startScrapingJob(request),
    onSuccess: () => {
      // Invalidate jobs list to show the new job
      queryClient.invalidateQueries({ queryKey: scrapingKeys.jobs() })
      queryClient.invalidateQueries({ queryKey: scrapingKeys.stats() })
    },
  })
}

export function useCancelScrapingJob() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (jobId: string) => mockScrapingApi.cancelJob(jobId),
    onSuccess: (_, jobId) => {
      // Update the specific job cache
      queryClient.setQueryData(
        scrapingKeys.job(jobId),
        (oldData: ScrapingJob | undefined) => {
          if (oldData) {
            return {
              ...oldData,
              status: 'cancelled' as const,
              completedAt: new Date(),
            }
          }
          return oldData
        }
      )
      
      // Invalidate jobs list
      queryClient.invalidateQueries({ queryKey: scrapingKeys.jobs() })
      queryClient.invalidateQueries({ queryKey: scrapingKeys.stats() })
    },
  })
}

export function useDiscoverUrls() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (sources: string[]) => mockScrapingApi.discoverUrls(sources),
    onSuccess: (data, sources) => {
      // Cache the discovered URLs for each source
      data.forEach((result) => {
        queryClient.setQueryData(
          scrapingKeys.discoveredUrls(result.source),
          [result]
        )
      })
    },
  })
}

// Utility hooks
export function usePrefetchScrapingJob(jobId: string) {
  const queryClient = useQueryClient()
  
  return () => {
    queryClient.prefetchQuery({
      queryKey: scrapingKeys.job(jobId),
      queryFn: () => mockScrapingApi.getJob(jobId),
      staleTime: 1000,
    })
  }
}

export function useInvalidateScrapingData() {
  const queryClient = useQueryClient()
  
  return () => {
    queryClient.invalidateQueries({ queryKey: scrapingKeys.all })
  }
}

// Real-time job monitoring hook
export function useJobMonitoring(jobId: string) {
  const { data: job, isLoading, error } = useScrapingJob(jobId)
  
  const isActive = job?.status === 'running' || job?.status === 'pending'
  const isCompleted = job?.status === 'completed'
  const isFailed = job?.status === 'failed'
  const isCancelled = job?.status === 'cancelled'
  
  const progressPercentage = job?.progress || 0
  const estimatedTimeRemaining = job && isActive ? 
    calculateEstimatedTime(job) : null
  
  return {
    job,
    isLoading,
    error,
    isActive,
    isCompleted,
    isFailed,
    isCancelled,
    progressPercentage,
    estimatedTimeRemaining,
  }
}

// Helper function to calculate estimated time remaining
function calculateEstimatedTime(job: ScrapingJob): number | null {
  if (!job.startedAt || job.processedUrls === 0) return null
  
  const elapsedTime = Date.now() - job.startedAt.getTime()
  const avgTimePerUrl = elapsedTime / job.processedUrls
  const remainingUrls = job.totalUrls - job.processedUrls
  
  return Math.round((avgTimePerUrl * remainingUrls) / 1000) // Return in seconds
}

// Batch operations hook
export function useBatchScrapingOperations() {
  const startJob = useStartScrapingJob()
  const cancelJob = useCancelScrapingJob()
  const discoverUrls = useDiscoverUrls()
  
  const startMultipleJobs = async (requests: ScrapingJobRequest[]) => {
    const results = await Promise.allSettled(
      requests.map(request => startJob.mutateAsync(request))
    )
    
    return results.map((result, index) => ({
      request: requests[index],
      success: result.status === 'fulfilled',
      jobId: result.status === 'fulfilled' ? result.value.jobId : null,
      error: result.status === 'rejected' ? result.reason : null,
    }))
  }
  
  const cancelMultipleJobs = async (jobIds: string[]) => {
    const results = await Promise.allSettled(
      jobIds.map(jobId => cancelJob.mutateAsync(jobId))
    )
    
    return results.map((result, index) => ({
      jobId: jobIds[index],
      success: result.status === 'fulfilled',
      error: result.status === 'rejected' ? result.reason : null,
    }))
  }
  
  return {
    startMultipleJobs,
    cancelMultipleJobs,
    isLoading: startJob.isPending || cancelJob.isPending || discoverUrls.isPending,
  }
}
