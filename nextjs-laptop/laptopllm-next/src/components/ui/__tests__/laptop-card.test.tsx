// Tests for LaptopCard component

import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { LaptopCard, LaptopCardSkeleton } from '../laptop-card'
import { AppProvider } from '@/contexts/app-context'
import type { LaptopData } from '@/lib/scraping/firecrawl.service'
import type { CompatibilityScore } from '@/lib/llm/compatibility.service'

// Mock Next.js components
vi.mock('next/image', () => ({
  default: ({ src, alt, ...props }: any) => <img src={src} alt={alt} {...props} />
}))

vi.mock('next/link', () => ({
  default: ({ href, children, ...props }: any) => <a href={href} {...props}>{children}</a>
}))

// Helper function to render with AppProvider
const renderWithProvider = (component: React.ReactElement) => {
  return render(
    <AppProvider>
      {component}
    </AppProvider>
  )
}

describe('LaptopCard', () => {
  const mockLaptop: LaptopData = {
    title: 'ASUS ROG Gaming Laptop',
    brand: 'ASUS',
    url: 'https://example.com/laptop1',
    source: 'example.com',
    scrapedAt: new Date(),
    price: { current: 1299, currency: 'USD' },
    specifications: {
      ram: '16GB DDR4',
      cpu: 'Intel Core i7-12700H 8-core 2.7GHz',
      gpu: 'NVIDIA GeForce RTX 3060 6GB',
      storage: '512GB NVMe SSD',
      display: '15.6 inch FHD',
    },
    image: 'https://example.com/laptop.jpg',
  }

  const mockCompatibilityScore: CompatibilityScore = {
    overall: 85,
    breakdown: {
      memory: 90,
      processing: 80,
      graphics: 85,
      storage: 90,
      thermals: 75,
    },
    estimatedPerformance: {
      tokensPerSecond: 45,
      memoryUsage: 12,
      powerConsumption: 150,
    },
    warnings: ['High power consumption during intensive tasks'],
    recommendations: ['Consider external cooling for extended use'],
  }

  describe('Basic rendering', () => {
    it('should render laptop information correctly', () => {
      renderWithProvider(<LaptopCard laptop={mockLaptop} />)

      expect(screen.getByText('ASUS ROG Gaming Laptop')).toBeInTheDocument()
      expect(screen.getByText('ASUS')).toBeInTheDocument()
      expect(screen.getByText('$1,299.00')).toBeInTheDocument()
      expect(screen.getByText('16GB DDR4')).toBeInTheDocument()
      expect(screen.getByText('Intel Core i7-12700H 8-core 2.7GHz')).toBeInTheDocument()
    })

    it('should render laptop image when provided', () => {
      renderWithProvider(<LaptopCard laptop={mockLaptop} />)

      const image = screen.getByAltText('ASUS ROG Gaming Laptop')
      expect(image).toBeInTheDocument()
      expect(image).toHaveAttribute('src', 'https://example.com/laptop.jpg')
    })

    it('should render without image when not provided', () => {
      const laptopWithoutImage = { ...mockLaptop, image: undefined }
      renderWithProvider(<LaptopCard laptop={laptopWithoutImage} />)

      expect(screen.queryByAltText('ASUS ROG Gaming Laptop')).not.toBeInTheDocument()
    })

    it('should render View Details button', () => {
      renderWithProvider(<LaptopCard laptop={mockLaptop} />)

      expect(screen.getByText('Details')).toBeInTheDocument()
    })

    it('should render external link to source', () => {
      renderWithProvider(<LaptopCard laptop={mockLaptop} />)

      const link = screen.getByText('View on example.com')
      expect(link).toBeInTheDocument()
      expect(link.closest('a')).toHaveAttribute('href', 'https://example.com/laptop1')
    })
  })

  describe('Price handling', () => {
    it('should format price correctly', () => {
      renderWithProvider(<LaptopCard laptop={mockLaptop} />)

      expect(screen.getByText('$1,299.00')).toBeInTheDocument()
    })

    it('should show original price when discounted', () => {
      const discountedLaptop = {
        ...mockLaptop,
        price: { current: 999, original: 1299, currency: 'USD' }
      }
      renderWithProvider(<LaptopCard laptop={discountedLaptop} />)

      expect(screen.getByText('$999.00')).toBeInTheDocument()
      expect(screen.getByText('$1,299.00')).toBeInTheDocument()
    })

    it('should handle missing price gracefully', () => {
      const laptopWithoutPrice = { ...mockLaptop, price: undefined }
      renderWithProvider(<LaptopCard laptop={laptopWithoutPrice} />)

      expect(screen.queryByText(/\$/)).not.toBeInTheDocument()
    })
  })

  describe('Compatibility score display', () => {
    it('should show compatibility score when provided and enabled', () => {
      renderWithProvider(
        <LaptopCard
          laptop={mockLaptop}
          compatibilityScore={mockCompatibilityScore}
          showCompatibility={true}
        />
      )

      expect(screen.getByText('LLM Compatibility')).toBeInTheDocument()
      expect(screen.getByText('85% (Excellent)')).toBeInTheDocument()
      expect(screen.getAllByText('90%')).toHaveLength(2) // Memory and Storage scores
      expect(screen.getByText('80%')).toBeInTheDocument() // Processing score
    })

    it('should hide compatibility score when showCompatibility is false', () => {
      renderWithProvider(
        <LaptopCard
          laptop={mockLaptop}
          compatibilityScore={mockCompatibilityScore}
          showCompatibility={false}
        />
      )

      expect(screen.queryByText('LLM Compatibility')).not.toBeInTheDocument()
    })

    it('should show performance estimate when available', () => {
      renderWithProvider(
        <LaptopCard
          laptop={mockLaptop}
          compatibilityScore={mockCompatibilityScore}
          showCompatibility={true}
        />
      )

      expect(screen.getByText('Est. 45 tokens/sec')).toBeInTheDocument()
    })

    it('should show warnings when available', () => {
      renderWithProvider(
        <LaptopCard
          laptop={mockLaptop}
          compatibilityScore={mockCompatibilityScore}
          showCompatibility={true}
        />
      )

      expect(screen.getByText('Considerations')).toBeInTheDocument()
      expect(screen.getByText('High power consumption during intensive tasks')).toBeInTheDocument()
    })

    it('should display correct compatibility labels', () => {
      const scores = [
        { score: 95, label: 'Excellent' },
        { score: 75, label: 'Good' },
        { score: 50, label: 'Fair' },
        { score: 30, label: 'Limited' },
      ]

      scores.forEach(({ score, label }) => {
        const scoreData = { ...mockCompatibilityScore, overall: score }
        const { unmount } = renderWithProvider(
          <LaptopCard
            laptop={mockLaptop}
            compatibilityScore={scoreData}
            showCompatibility={true}
          />
        )

        expect(screen.getByText(`${score}% (${label})`)).toBeInTheDocument()
        unmount()
      })
    })
  })

  describe('Specifications display', () => {
    it('should show all available specifications', () => {
      renderWithProvider(<LaptopCard laptop={mockLaptop} />)

      expect(screen.getByText('Key Specs')).toBeInTheDocument()
      expect(screen.getByText(/RAM:/)).toBeInTheDocument()
      expect(screen.getByText(/CPU:/)).toBeInTheDocument()
      expect(screen.getByText(/GPU:/)).toBeInTheDocument()
      expect(screen.getByText(/Storage:/)).toBeInTheDocument()
    })

    it('should handle missing specifications gracefully', () => {
      const laptopWithoutSpecs = { ...mockLaptop, specifications: undefined }
      renderWithProvider(<LaptopCard laptop={laptopWithoutSpecs} />)

      expect(screen.queryByText('Key Specs')).not.toBeInTheDocument()
    })

    it('should handle partial specifications', () => {
      const laptopWithPartialSpecs = {
        ...mockLaptop,
        specifications: { ram: '8GB', cpu: 'Intel i5' }
      }
      renderWithProvider(<LaptopCard laptop={laptopWithPartialSpecs} />)

      expect(screen.getByText('8GB')).toBeInTheDocument()
      expect(screen.getByText('Intel i5')).toBeInTheDocument()
      expect(screen.queryByText(/GPU:/)).not.toBeInTheDocument()
    })
  })

  describe('Event handlers', () => {
    it('should call onViewDetails when Details button is clicked', () => {
      const onViewDetails = vi.fn()
      renderWithProvider(<LaptopCard laptop={mockLaptop} onViewDetails={onViewDetails} />)

      fireEvent.click(screen.getByText('Details'))
      expect(onViewDetails).toHaveBeenCalledWith(mockLaptop)
    })

    it('should have favorite and comparison buttons', () => {
      renderWithProvider(<LaptopCard laptop={mockLaptop} />)

      // Should have multiple buttons including favorite, comparison, and details
      const buttons = screen.getAllByRole('button')
      expect(buttons.length).toBeGreaterThanOrEqual(3) // favorite, compare, details buttons
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels and structure', () => {
      renderWithProvider(<LaptopCard laptop={mockLaptop} />)

      // Check for proper heading structure (CardTitle renders as h3)
      expect(screen.getByText('ASUS ROG Gaming Laptop')).toBeInTheDocument()

      // Check for proper button roles
      expect(screen.getByRole('button', { name: 'Details' })).toBeInTheDocument()

      // Check for proper link
      expect(screen.getByRole('link', { name: /View on example.com/ })).toBeInTheDocument()
    })
  })

  describe('Custom styling', () => {
    it('should apply custom className', () => {
      const { container } = renderWithProvider(
        <LaptopCard laptop={mockLaptop} className="custom-class" />
      )

      expect(container.firstChild).toHaveClass('custom-class')
    })
  })
})

describe('LaptopCardSkeleton', () => {
  it('should render skeleton loading state', () => {
    const { container } = render(<LaptopCardSkeleton />)

    // Check for skeleton elements with animation
    const skeletonElements = container.querySelectorAll('.animate-pulse')
    expect(skeletonElements.length).toBeGreaterThan(0)
  })

  it('should have proper structure matching the main card', () => {
    const { container } = render(<LaptopCardSkeleton />)

    // Should have card structure
    expect(container.querySelector('[class*="flex"][class*="flex-col"]')).toBeInTheDocument()
  })
})
