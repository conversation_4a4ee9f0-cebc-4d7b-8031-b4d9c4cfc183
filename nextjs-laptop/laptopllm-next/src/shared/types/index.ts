// Re-export Prisma types
export type {
  laptops as Laptop,
  brands as Brand,
  cpus as CPU,
  gpus as GPU,
  llm_models as LLMModel,
  laptop_llm_compatibility as LaptopLLMCompatibility,
  ScrapingSource,
  ScrapingJob,
  ScrapingLog,
  ScrapingStatus,
  LogLevel,
} from '../../../generated/prisma'

// Domain-specific types
export interface LaptopSpecifications {
  cpu: {
    model: string
    cores: number
    threads: number
    baseClockGhz: number
    boostClockGhz?: number
    architecture: string
  }
  ram: {
    sizeGb: number
    type: string
    speed: number
    expandable: boolean
    maxSupportedGb?: number
  }
  gpu?: {
    model: string
    vramGb: number
    memoryType: string
    isDiscrete: boolean
  }
  storage: {
    type: string
    capacityGb: number
    interface: string
    readSpeedMbps?: number
    writeSpeedMbps?: number
  }[]
  display: {
    sizeInches: number
    resolution: {
      width: number
      height: number
      name: string
    }
    refreshRate?: number
    panelType?: string
    isTouchscreen: boolean
  }
  physical: {
    weightKg: number
    dimensions: {
      heightMm: number
      widthMm: number
      depthMm: number
    }
    material?: string
    color?: string
  }
}

export interface LLMCompatibilityScore {
  laptopId: number
  llmId: number
  overallScore: number
  canRunOffline: boolean
  estimatedTokensPerSecond?: number
  maxContextLength?: number
  recommendedBatchSize?: number
  estimatedMemoryUsageGb?: number
  qualitativeAssessment?: string
  breakdown: {
    cpuScore: number
    ramScore: number
    gpuScore: number
    storageScore: number
  }
}

export interface ScrapedLaptopData {
  title: string
  brand?: string
  model?: string
  price: number
  currency: string
  url: string
  imageUrls: string[]
  description?: string
  specifications?: Partial<LaptopSpecifications>
  sourceId: string
  scrapedAt: Date
}

export interface SearchFilters {
  brands?: string[]
  priceRange?: {
    min: number
    max: number
  }
  ramRange?: {
    min: number
    max: number
  }
  storageRange?: {
    min: number
    max: number
  }
  cpuBrands?: string[]
  gpuBrands?: string[]
  displaySizes?: number[]
  llmCompatibility?: {
    modelId: number
    minScore: number
  }
  sortBy?: 'price' | 'compatibility' | 'performance' | 'newest'
  sortOrder?: 'asc' | 'desc'
}

export interface PaginationParams {
  page: number
  limit: number
}

export interface SearchResults<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: {
    message: string
    code?: string
    details?: any
  }
}

export interface ScrapingConfig {
  sourceId: string
  rateLimitMs: number
  maxRetries: number
  timeoutMs: number
  selectors: Record<string, string>
  headers?: Record<string, string>
  userAgent?: string
  requiresFullBrowserAutomation: boolean
}

export interface DataQualityMetrics {
  completeness: number
  accuracy: number
  consistency: number
  duplicateRate: number
  errorRate: number
  lastUpdated: Date
}

// ============================================================================
// ADDITIONAL DOMAIN TYPES
// ============================================================================

export interface LaptopWithCompatibility {
  laptop: Laptop
  compatibility?: LLMCompatibilityScore[]
  averageCompatibilityScore?: number
  bestCompatibleModels?: LLMModel[]
}

export interface LaptopSearchResult extends LaptopWithCompatibility {
  relevanceScore?: number
  priceHistory?: PricePoint[]
  availability?: AvailabilityInfo
}

export interface PricePoint {
  price: number
  currency: string
  source: string
  timestamp: Date
  url?: string
}

export interface AvailabilityInfo {
  inStock: boolean
  stockLevel?: 'High' | 'Medium' | 'Low' | 'Out of Stock'
  estimatedDelivery?: Date
  source: string
  lastChecked: Date
}

export interface LLMModelWithRequirements extends LLMModel {
  requirements: {
    minRamGb: number
    recommendedRamGb: number
    minStorageGb: number
    minCpuCores: number
    requiresGpu: boolean
    minGpuVramGb?: number
  }
  performance: {
    tokensPerSecondEstimate: number
    maxContextLength: number
    quantizationOptions: string[]
  }
}

export interface ScrapingJobResult {
  jobId: string
  status: ScrapingStatus
  startedAt: Date
  completedAt?: Date
  itemsProcessed: number
  itemsSuccessful: number
  itemsFailed: number
  errors: string[]
  data?: ScrapedLaptopData[]
}

export interface UserPreferences {
  budget?: {
    min: number
    max: number
    currency: string
  }
  useCases: string[]
  preferredBrands: string[]
  requiredFeatures: string[]
  dealBreakers: string[]
  llmModelsOfInterest: string[]
}

export interface RecommendationRequest {
  userPreferences: UserPreferences
  filters?: SearchFilters
  maxResults?: number
  includeAlternatives?: boolean
}

export interface LaptopRecommendation {
  laptop: LaptopWithCompatibility
  score: number
  reasoning: string[]
  pros: string[]
  cons: string[]
  alternativeOptions?: LaptopWithCompatibility[]
}
