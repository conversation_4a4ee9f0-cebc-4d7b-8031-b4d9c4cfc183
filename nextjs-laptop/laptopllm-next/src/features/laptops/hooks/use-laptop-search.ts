import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useState, useCallback, useMemo } from 'react'
import { laptopService } from '../services/laptop-service'
import type { LaptopSearchParams, LaptopSearchResult, LaptopFilters } from '../types'

/**
 * Hook for laptop search functionality
 */
export function useLaptopSearch(initialParams?: LaptopSearchParams) {
  const queryClient = useQueryClient()
  const [searchParams, setSearchParams] = useState<LaptopSearchParams>(
    initialParams || {
      page: 1,
      limit: 12,
      sortBy: 'compatibility',
      sortOrder: 'desc',
    }
  )

  // Query key factory
  const queryKey = useMemo(
    () => ['laptops', 'search', searchParams],
    [searchParams]
  )

  // Main search query
  const {
    data: searchResult,
    isLoading,
    isError,
    error,
    refetch,
    isFetching,
  } = useQuery<LaptopSearchResult>({
    queryKey,
    queryFn: () => laptopService.searchLaptops(searchParams),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    placeholderData: (previousData) => previousData,
  })

  // Update search query
  const updateQuery = useCallback((query: string) => {
    setSearchParams(prev => ({
      ...prev,
      query,
      page: 1, // Reset to first page
    }))
  }, [])

  // Update filters
  const updateFilters = useCallback((filters: Partial<LaptopFilters>) => {
    setSearchParams(prev => ({
      ...prev,
      filters: {
        ...prev.filters,
        ...filters,
      },
      page: 1, // Reset to first page
    }))
  }, [])

  // Clear filters
  const clearFilters = useCallback(() => {
    setSearchParams(prev => ({
      ...prev,
      filters: {},
      page: 1,
    }))
  }, [])

  // Update sorting
  const updateSort = useCallback((sortBy: string, sortOrder: 'asc' | 'desc' = 'desc') => {
    setSearchParams(prev => ({
      ...prev,
      sortBy: sortBy as any,
      sortOrder,
      page: 1, // Reset to first page
    }))
  }, [])

  // Change page
  const changePage = useCallback((page: number) => {
    setSearchParams(prev => ({
      ...prev,
      page,
    }))
  }, [])

  // Change items per page
  const changeLimit = useCallback((limit: number) => {
    setSearchParams(prev => ({
      ...prev,
      limit,
      page: 1, // Reset to first page
    }))
  }, [])

  // Prefetch next page
  const prefetchNextPage = useCallback(() => {
    if (searchResult?.hasMore) {
      const nextPageParams = {
        ...searchParams,
        page: (searchParams.page || 1) + 1,
      }
      
      queryClient.prefetchQuery({
        queryKey: ['laptops', 'search', nextPageParams],
        queryFn: () => laptopService.searchLaptops(nextPageParams),
        staleTime: 5 * 60 * 1000,
      })
    }
  }, [queryClient, searchParams, searchResult?.hasMore])

  // Reset search
  const resetSearch = useCallback(() => {
    setSearchParams({
      page: 1,
      limit: 12,
      sortBy: 'compatibility',
      sortOrder: 'desc',
    })
  }, [])

  // Get active filter count
  const activeFilterCount = useMemo(() => {
    if (!searchParams.filters) return 0
    
    return Object.values(searchParams.filters).filter(value => {
      if (Array.isArray(value)) return value.length > 0
      if (typeof value === 'object' && value !== null) {
        return Object.values(value).some(v => v !== undefined && v !== null)
      }
      return value !== undefined && value !== null
    }).length
  }, [searchParams.filters])

  // Check if search has results
  const hasResults = useMemo(() => {
    return searchResult && searchResult.total > 0
  }, [searchResult])

  // Check if search is empty (no query or filters)
  const isEmpty = useMemo(() => {
    return !searchParams.query && activeFilterCount === 0
  }, [searchParams.query, activeFilterCount])

  return {
    // Data
    laptops: searchResult?.laptops || [],
    total: searchResult?.total || 0,
    page: searchResult?.page || 1,
    totalPages: searchResult?.totalPages || 1,
    hasMore: searchResult?.hasMore || false,
    
    // State
    searchParams,
    isLoading,
    isError,
    error,
    isFetching,
    hasResults,
    isEmpty,
    activeFilterCount,
    
    // Actions
    updateQuery,
    updateFilters,
    clearFilters,
    updateSort,
    changePage,
    changeLimit,
    resetSearch,
    refetch,
    prefetchNextPage,
  }
}

/**
 * Hook for laptop search suggestions
 */
export function useLaptopSearchSuggestions(query: string, enabled = true) {
  return useQuery({
    queryKey: ['laptops', 'suggestions', query],
    queryFn: async () => {
      if (!query || query.length < 2) return []
      
      // This would call a suggestions API endpoint
      const response = await fetch(`/api/laptops/suggestions?q=${encodeURIComponent(query)}`)
      if (!response.ok) throw new Error('Failed to fetch suggestions')
      return response.json()
    },
    enabled: enabled && query.length >= 2,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook for popular search terms
 */
export function usePopularSearches() {
  return useQuery({
    queryKey: ['laptops', 'popular-searches'],
    queryFn: async () => {
      const response = await fetch('/api/laptops/popular-searches')
      if (!response.ok) throw new Error('Failed to fetch popular searches')
      return response.json()
    },
    staleTime: 60 * 60 * 1000, // 1 hour
    gcTime: 2 * 60 * 60 * 1000, // 2 hours
  })
}
