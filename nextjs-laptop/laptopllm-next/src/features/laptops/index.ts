/**
 * Laptops feature exports
 * Centralized exports for the laptops feature module
 */

// Types
export type * from './types'

// Services
export { laptopService } from './services/laptop-service'

// Hooks
export { 
  useLaptopSearch,
  useLaptopSearchSuggestions,
  usePopularSearches 
} from './hooks/use-laptop-search'

export {
  useLaptopDetails,
  useSimilarLaptops,
  useLaptopAvailability,
  useLaptopPriceHistory,
  useLaptopsByIds,
  useLaptopComparison,
  useTrendingLaptops,
  useFeaturedLaptops,
  useLaptopStats
} from './hooks/use-laptop-details'

// Components
export { LaptopCard } from './components/laptop-card'

// Utils (to be created)
export * from './utils'
