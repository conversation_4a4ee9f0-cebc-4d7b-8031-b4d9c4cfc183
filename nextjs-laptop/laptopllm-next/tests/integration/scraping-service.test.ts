import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { ScrapingService } from '@/lib/scraping/scraping-service'
import { FirecrawlService } from '@/lib/scraping/firecrawl.service'
import { PuppeteerService } from '@/lib/scraping/puppeteer.service'
import type { ScrapingConfig, ScrapingResult } from '@/features/scraper/types/index'

// Mock the scrapers
vi.mock('@/lib/scraping/firecrawl.service')
vi.mock('@/lib/scraping/puppeteer.service')

describe('ScrapingService Integration Tests', () => {
  let scrapingService: ScrapingService
  let mockFirecrawlService: any
  let mockPuppeteerService: any

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks()
    
    // Create mock instances
    mockFirecrawlService = {
      scrape: vi.fn(),
      scrapeMultiple: vi.fn(),
      isHealthy: vi.fn().mockResolvedValue(true),
    }

    mockPuppeteerService = {
      scrape: vi.fn(),
      scrapeMultiple: vi.fn(),
      isHealthy: vi.fn().mockResolvedValue(true),
    }

    // Mock constructor returns
    vi.mocked(FirecrawlService).mockImplementation(() => mockFirecrawlService)
    vi.mocked(PuppeteerService).mockImplementation(() => mockPuppeteerService)

    scrapingService = new ScrapingService()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Single URL Scraping', () => {
    it('should successfully scrape with primary scraper (Firecrawl)', async () => {
      const mockResult: ScrapingResult = {
        success: true,
        data: {
          url: 'https://example.com/laptop1',
          title: 'Test Laptop',
          content: 'Laptop content',
          metadata: {
            price: '$999',
            brand: 'TestBrand',
            model: 'TestModel',
          },
          scrapedAt: new Date(),
        },
        scraper: 'firecrawl',
        duration: 1500,
      }

      mockFirecrawlService.scrape.mockResolvedValue(mockResult)

      const config: ScrapingConfig = {
        url: 'https://example.com/laptop1',
        selectors: {
          title: 'h1',
          price: '.price',
          specs: '.specifications',
        },
        waitFor: 2000,
      }

      const result = await scrapingService.scrape(config)

      expect(result.success).toBe(true)
      expect(result.data?.title).toBe('Test Laptop')
      expect(result.scraper).toBe('firecrawl')
      expect(mockFirecrawlService.scrape).toHaveBeenCalledWith(config)
      expect(mockPuppeteerService.scrape).not.toHaveBeenCalled()
    })

    it('should fallback to secondary scraper when primary fails', async () => {
      const primaryError: ScrapingResult = {
        success: false,
        error: 'Firecrawl failed',
        scraper: 'firecrawl',
        duration: 1000,
      }

      const fallbackResult: ScrapingResult = {
        success: true,
        data: {
          url: 'https://example.com/laptop1',
          title: 'Test Laptop (Puppeteer)',
          content: 'Laptop content from Puppeteer',
          metadata: {
            price: '$999',
            brand: 'TestBrand',
            model: 'TestModel',
          },
          scrapedAt: new Date(),
        },
        scraper: 'puppeteer',
        duration: 2500,
      }

      mockFirecrawlService.scrape.mockResolvedValue(primaryError)
      mockPuppeteerService.scrape.mockResolvedValue(fallbackResult)

      const config: ScrapingConfig = {
        url: 'https://example.com/laptop1',
        selectors: {
          title: 'h1',
          price: '.price',
        },
      }

      const result = await scrapingService.scrape(config)

      expect(result.success).toBe(true)
      expect(result.data?.title).toBe('Test Laptop (Puppeteer)')
      expect(result.scraper).toBe('puppeteer')
      expect(mockFirecrawlService.scrape).toHaveBeenCalledWith(config)
      expect(mockPuppeteerService.scrape).toHaveBeenCalledWith(config)
    })

    it('should fail when both scrapers fail', async () => {
      const primaryError: ScrapingResult = {
        success: false,
        error: 'Firecrawl failed',
        scraper: 'firecrawl',
        duration: 1000,
      }

      const secondaryError: ScrapingResult = {
        success: false,
        error: 'Puppeteer failed',
        scraper: 'puppeteer',
        duration: 1500,
      }

      mockFirecrawlService.scrape.mockResolvedValue(primaryError)
      mockPuppeteerService.scrape.mockResolvedValue(secondaryError)

      const config: ScrapingConfig = {
        url: 'https://example.com/laptop1',
        selectors: {
          title: 'h1',
        },
      }

      const result = await scrapingService.scrape(config)

      expect(result.success).toBe(false)
      expect(result.error).toContain('All scrapers failed')
      expect(mockFirecrawlService.scrape).toHaveBeenCalledWith(config)
      expect(mockPuppeteerService.scrape).toHaveBeenCalledWith(config)
    })
  })

  describe('Multiple URL Scraping', () => {
    it('should successfully scrape multiple URLs', async () => {
      const mockResults: ScrapingResult[] = [
        {
          success: true,
          data: {
            url: 'https://example.com/laptop1',
            title: 'Laptop 1',
            content: 'Content 1',
            metadata: { price: '$999' },
            scrapedAt: new Date(),
          },
          scraper: 'firecrawl',
          duration: 1500,
        },
        {
          success: true,
          data: {
            url: 'https://example.com/laptop2',
            title: 'Laptop 2',
            content: 'Content 2',
            metadata: { price: '$1299' },
            scrapedAt: new Date(),
          },
          scraper: 'firecrawl',
          duration: 1800,
        },
      ]

      mockFirecrawlService.scrapeMultiple.mockResolvedValue(mockResults)

      const configs: ScrapingConfig[] = [
        {
          url: 'https://example.com/laptop1',
          selectors: { title: 'h1', price: '.price' },
        },
        {
          url: 'https://example.com/laptop2',
          selectors: { title: 'h1', price: '.price' },
        },
      ]

      const results = await scrapingService.scrapeMultiple(configs)

      expect(results).toHaveLength(2)
      expect(results[0].success).toBe(true)
      expect(results[1].success).toBe(true)
      expect(results[0].data?.title).toBe('Laptop 1')
      expect(results[1].data?.title).toBe('Laptop 2')
      expect(mockFirecrawlService.scrapeMultiple).toHaveBeenCalledWith(configs)
    })

    it('should handle mixed success and failure in batch scraping', async () => {
      const mockResults: ScrapingResult[] = [
        {
          success: true,
          data: {
            url: 'https://example.com/laptop1',
            title: 'Laptop 1',
            content: 'Content 1',
            metadata: { price: '$999' },
            scrapedAt: new Date(),
          },
          scraper: 'firecrawl',
          duration: 1500,
        },
        {
          success: false,
          error: 'Failed to scrape laptop2',
          scraper: 'firecrawl',
          duration: 1000,
        },
      ]

      mockFirecrawlService.scrapeMultiple.mockResolvedValue(mockResults)

      const configs: ScrapingConfig[] = [
        {
          url: 'https://example.com/laptop1',
          selectors: { title: 'h1', price: '.price' },
        },
        {
          url: 'https://example.com/laptop2',
          selectors: { title: 'h1', price: '.price' },
        },
      ]

      const results = await scrapingService.scrapeMultiple(configs)

      expect(results).toHaveLength(2)
      expect(results[0].success).toBe(true)
      expect(results[1].success).toBe(false)
      expect(results[0].data?.title).toBe('Laptop 1')
      expect(results[1].error).toBe('Failed to scrape laptop2')
    })
  })

  describe('Health Checks', () => {
    it('should return healthy when all scrapers are healthy', async () => {
      mockFirecrawlService.isHealthy.mockResolvedValue(true)
      mockPuppeteerService.isHealthy.mockResolvedValue(true)

      const health = await scrapingService.getHealth()

      expect(health.healthy).toBe(true)
      expect(health.scrapers.firecrawl).toBe(true)
      expect(health.scrapers.puppeteer).toBe(true)
    })

    it('should return unhealthy when primary scraper is down', async () => {
      mockFirecrawlService.isHealthy.mockResolvedValue(false)
      mockPuppeteerService.isHealthy.mockResolvedValue(true)

      const health = await scrapingService.getHealth()

      expect(health.healthy).toBe(false)
      expect(health.scrapers.firecrawl).toBe(false)
      expect(health.scrapers.puppeteer).toBe(true)
    })

    it('should return unhealthy when all scrapers are down', async () => {
      mockFirecrawlService.isHealthy.mockResolvedValue(false)
      mockPuppeteerService.isHealthy.mockResolvedValue(false)

      const health = await scrapingService.getHealth()

      expect(health.healthy).toBe(false)
      expect(health.scrapers.firecrawl).toBe(false)
      expect(health.scrapers.puppeteer).toBe(false)
    })
  })

  describe('Rate Limiting', () => {
    it('should respect rate limiting for multiple requests', async () => {
      const mockResult: ScrapingResult = {
        success: true,
        data: {
          url: 'https://example.com/laptop1',
          title: 'Test Laptop',
          content: 'Content',
          metadata: {},
          scrapedAt: new Date(),
        },
        scraper: 'firecrawl',
        duration: 1000,
      }

      mockFirecrawlService.scrape.mockResolvedValue(mockResult)

      const config: ScrapingConfig = {
        url: 'https://example.com/laptop1',
        selectors: { title: 'h1' },
      }

      const startTime = Date.now()

      // Make multiple requests
      await Promise.all([
        scrapingService.scrape(config),
        scrapingService.scrape(config),
        scrapingService.scrape(config),
      ])

      const endTime = Date.now()
      const duration = endTime - startTime

      // Should take at least some time due to rate limiting
      expect(duration).toBeGreaterThan(100)
      expect(mockFirecrawlService.scrape).toHaveBeenCalledTimes(3)
    })
  })
})
